# 附近查询接口重构 - URL变化对比

## 重构概述

为了避免MapController文件过于庞大，将所有与"附近查询"相关的接口抽离到新的`NearbyQueryController`中。

## URL变化对比表

| 功能描述 | 修改前URL | 修改后URL | 请求方式 | 状态 |
|---------|-----------|-----------|----------|------|
| 查询附近线路信息 | `GET /map/nearbyLines/{feederId}` | `GET /nearby/lines/{feederId}` | GET | ✅ 已迁移 |
| 查询附近变电站信息 | `GET /map/nearbySubstations/{feederId}` | `GET /nearby/substations/{feederId}` | GET | ✅ 已迁移 |
| 查询附近变电站(内网) | `GET /map/nearbySubstationsByNet/{feederId}/{bufferRadius}` | `GET /nearby/substations/{feederId}/{bufferRadius}` | GET | ✅ 已迁移 |
| 查询附近设备 | `GET /map/nearbyDevices/{feederId}/{bufferRadius}` | `GET /nearby/devices/{feederId}/{bufferRadius}` | GET | ✅ 已迁移 |
| 根据坐标查询附近设备 | `POST /map/nearbyDevicesByPoint` | `POST /nearby/devices/byPoint` | POST | ✅ 已迁移 |
| 根据坐标查询附近变电站 | `POST /map/nearbySubstationsByPoint` | `POST /nearby/substations/byPoint` | POST | ✅ 已迁移 |
| 计算设备距离信息 | `POST /map/calculateDeviceDistanceInfo` | `POST /nearby/devices/distance` | POST | ✅ 已迁移 |

## 保留在MapController中的接口

以下接口仍保留在MapController中，因为它们与地图核心功能相关：

| 功能描述 | URL | 请求方式 |
|---------|-----|----------|
| 查询线路指定范围内的线路 | `POST /map/feederRangeQuery` | POST |
| 根据杆塔id查询两端导线 | `GET /map/selectWireEnd/{psrId}` | GET |
| 判断设备真正psrId | `GET /map/selectPsrId/{psrId}/{type}` | GET |
| 查询运方调整转供路径 | `GET /map/transferSupply/{feederId}/{closeId}/{openId}` | GET |
| 获取联络开关信息列表 | `GET /map/contactSwitchInfo/{feederId}` | GET |
| 计算负载率变化 | `GET /map/calculateLoadRateChange/{feederId}/{closeId}/{openId}` | GET |

## 新增的NearbyQueryController

### 控制器信息
- **文件路径**: `smart-grid-api/src/main/java/com/ruoyi/controller/map/NearbyQueryController.java`
- **请求映射**: `@RequestMapping("/nearby")`
- **功能**: 专门处理所有附近查询相关的接口

### 服务层变化
- **接口**: `INearbyQueryService` - 新增了缺失的方法声明
- **实现**: `NearbyQueryService` - 通过委托给`IMapService`实现新增的方法

## 详细URL变化示例

### 1. 查询附近线路信息
```bash
# 修改前
GET /map/nearbyLines/FEEDER_001?radius=5000

# 修改后  
GET /nearby/lines/FEEDER_001?radius=5000
```

### 2. 查询附近变电站信息
```bash
# 修改前
GET /map/nearbySubstations/FEEDER_001

# 修改后
GET /nearby/substations/FEEDER_001
```

### 3. 查询附近变电站(内网接口)
```bash
# 修改前
GET /map/nearbySubstationsByNet/FEEDER_001/5000

# 修改后
GET /nearby/substations/FEEDER_001/5000
```

### 4. 查询附近设备
```bash
# 修改前
GET /map/nearbyDevices/FEEDER_001/3000

# 修改后
GET /nearby/devices/FEEDER_001/3000
```

### 5. 根据坐标点查询附近设备
```bash
# 修改前
POST /map/nearbyDevicesByPoint
Content-Type: application/json
{
    "lng": 120.123456,
    "lat": 30.123456,
    "bufferRadius": 5000.0,
    "feederId": "FEEDER_001"
}

# 修改后
POST /nearby/devices/byPoint
Content-Type: application/json
{
    "lng": 120.123456,
    "lat": 30.123456,
    "bufferRadius": 5000.0,
    "feederId": "FEEDER_001"
}
```

### 6. 根据坐标点查询附近变电站
```bash
# 修改前
POST /map/nearbySubstationsByPoint
Content-Type: application/json
{
    "lng": 120.123456,
    "lat": 30.123456,
    "bufferRadius": 5000.0
}

# 修改后
POST /nearby/substations/byPoint
Content-Type: application/json
{
    "lng": 120.123456,
    "lat": 30.123456,
    "bufferRadius": 5000.0
}
```

### 7. 计算设备距离信息
```bash
# 修改前
POST /map/calculateDeviceDistanceInfo
Content-Type: application/json
{
    "aLng": 120.123456,
    "aLat": 30.123456,
    "bLng": 120.234567,
    "bLat": 30.234567,
    "bfeederId": "FEEDER_002",
    "token": "your_token_here"
}

# 修改后
POST /nearby/devices/distance
Content-Type: application/json
{
    "aLng": 120.123456,
    "aLat": 30.123456,
    "bLng": 120.234567,
    "bLat": 30.234567,
    "bfeederId": "FEEDER_002",
    "token": "your_token_here"
}
```

## 迁移影响

### 对前端的影响
- 需要更新所有相关的API调用URL
- 建议使用配置文件管理API基础路径，便于统一修改

### 对其他服务的影响
- 如果有其他微服务调用这些接口，需要同步更新URL
- 建议通过服务发现或配置中心管理API地址

### 兼容性考虑
- 可以考虑在MapController中保留旧接口一段时间，通过`@Deprecated`标记并重定向到新接口
- 或者通过网关层进行URL重写实现向后兼容

## 重构优势

1. **代码组织更清晰**: 将相关功能聚合到专门的控制器中
2. **文件大小控制**: 避免单个控制器文件过于庞大
3. **职责分离**: MapController专注于地图核心功能，NearbyQueryController专注于附近查询
4. **维护性提升**: 相关功能集中，便于维护和扩展
5. **URL语义化**: 新的URL结构更加语义化和RESTful

## 注意事项

1. 确保所有调用方都已更新URL
2. 测试所有迁移的接口功能正常
3. 更新相关的API文档
4. 考虑是否需要保留旧接口的兼容性
