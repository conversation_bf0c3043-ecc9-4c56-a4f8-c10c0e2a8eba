package com.ruoyi.controller.map;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.calc.FeederTransferCapVo;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.service.map.IMapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

/**
 * 地图
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/map")
@SaIgnore
public class MapController extends BaseController {
    @Autowired
    IMapService mapService;


    /**
     * 查询一个线路指定范围内的线路
     */
    @PostMapping("/feederRangeQuery")
    public R<List<NeedFeederVo>> feederRangeQuery(@RequestBody FeederRangeQueryBo feederRangeQueryBo) {
        return R.ok(mapService.feederRangeQuery(feederRangeQueryBo));
    }


    /**
     * 根据杆塔id查询两端导线
     */
    @GetMapping("/selectWireEnd/{psrId}")
    public R<List<WireEndVo>> selectWireEnd(@PathVariable String psrId) {
        return R.ok(mapService.selectWireEnd(psrId));
    }

    /**
     * id和设备类型判断他的真正psrId
     */
    @GetMapping("/selectPsrId/{psrId}/{type}")
    public R<String> selectPsrId(@PathVariable String psrId, @PathVariable String type) {
        return R.ok("200", mapService.selectPsrId(psrId, type));
    }

    /**
     * 查询运方调整转供路径
     */
    @GetMapping("/transferSupply/{feederId}/{closeId}/{openId}")
    public R<List<NodeVo>> transferSupply(@PathVariable String feederId, @PathVariable String closeId, @PathVariable String openId) {
        return R.ok(mapService.transferSupply(feederId, closeId, openId));
    }

    /**
     * 根据线路ID获取联络开关信息列表
     *
     * @param feederId 线路ID
     * @return 联络开关信息列表
     */
    @GetMapping("/contactSwitchInfo/{feederId}")
    public R<List<ContactSwitchInfoVo>> getContactSwitchInfoByFeederId(@PathVariable String feederId) {
        List<ContactSwitchInfoVo> result = mapService.getContactSwitchInfo(feederId);
        return R.ok(result);
    }

    /**
     * 计算基于转供路径的负载率变化
     * 根据转供路径计算当前线路转供至另一条线路的负载率变化
     *
     * @param feederId 源线路ID
     * @param closeId  联络开关ID（需要闭合的开关）
     * @param openId   主干开关ID（需要断开的开关）
     * @return 负载率变化计算结果
     */
    @GetMapping("/calculateLoadRateChange/{feederId}/{closeId}/{openId}")
    public R<FeederTransferCapVo> calculateLoadRateChange(@PathVariable String feederId, @PathVariable String closeId, @PathVariable String openId) {
        FeederTransferCap feederTransferCap = mapService.calculateLoadRateChange(feederId, closeId, openId);
        return R.ok(feederTransferCap.toFTrVo());
    }



}
