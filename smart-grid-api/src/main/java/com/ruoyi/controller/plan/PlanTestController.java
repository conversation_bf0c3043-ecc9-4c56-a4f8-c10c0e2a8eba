package com.ruoyi.controller.plan;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.bo.QueryDevBo;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.plan.TestService;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.IPlanTestService;
import com.ruoyi.service.text.TrendTextCalcServiceImpl;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.entity.psm.HttpUtils;
import com.ruoyi.entity.psm.AmapSdkCommon;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 方案
 */
@RestController
@RequestMapping("/planTest")
@SaIgnore
public class PlanTestController {

    @Autowired
    IPlanTestService iPlanTestService;


    @Autowired
    TrendTextCalcServiceImpl textTrendCalcService;

    @Autowired
    TestService testService;

    @Autowired
    IMapService mapService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 用于测试线段
     */
    @GetMapping("/getSegList")
    public R<ArrayList<SegBetweenVo>> getSegList(@RequestParam String feederId, @RequestParam(required = false) String deviceId) {

        ArrayList<SegBetween> segBetweenList = iPlanTestService.getSegBetweenList(feederId, deviceId);
        return R.ok(NodeUtils.toSegBetweenVoList(segBetweenList));
    }

    /**
     * 用于测试线段
     */
    @GetMapping("/getQueryDevs")
    public R<List<QueryDevBo>> getSegList() {
        return R.ok(testService.query());
    }

    /**
     * 获取联络开关
     */
    @GetMapping("/getContactKgs")
    public R<ArrayList<NodeVo>> getContactKgs(@RequestParam String feederId) {

        ArrayList<Node> result = iPlanTestService.getContactKgs(feederId);
        return R.ok(NodeUtils.toNodeVos(result));
    }

    /**
     * 获取联络开关对应各个主干路径都主干开关的各个分段
     */
    @GetMapping("/getAllContactKgSegList")
    public R<List<HashMap<String, Object>>> getAllContactKgSegList(@RequestParam String feederId) {
        return R.ok(iPlanTestService.getAllContactKgSegList(feederId));
    }

    /**
     * 获取主干路径
     */
    @GetMapping("/getMainPath")
    public R<List<NodeVo>> getMainPath(@RequestParam String feederId) {
        return R.ok(NodeUtils.toNodeVos(iPlanTestService.getMainPath(feederId)));
    }

    @Resource
    private FeederDeviceMapper feederDeviceMapper;
    /**
     * 测试方法
     * 获取指定的feederId的地理位置，然后将地理位置转换为魔卡托
     * 获取10DKX-466641
     * 10DKX-670433
     * 这些指定路线
     */
    @GetMapping("/test")
    public void test() throws Exception {
        LambdaQueryWrapper<DeviceFeeder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceFeeder::getPsrId, "10DKX-53969","10DKX-453297","10DKX-368801","10DKX-284193","10DKX-300705","10DKX-180321","10DKX-463361",
                "10DKX-570961","10DKX-274225","10DKX-340673","10DKX-142769","10DKX-172449");

        for (DeviceFeeder deviceFeeder : feederDeviceMapper.selectList(queryWrapper)) {
            String geoList = deviceFeeder.getGeoList();
            List<List<double[]>> split = CoordinateConverter.split(geoList);


            // 杆塔、环网柜、开关站默认半径时1.5公里   变电站就默认3公里
            List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(split, 1500);
            List<List<Double>> bdz = BufferPolygonCreator.createBufferPolygon(split, 3000);
            // 判断收尾经纬度是否一致 不一致将头 添加到尾部
            if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                bufferPolygon.add(bufferPolygon.get(0));
            }
            if (!bdz.get(0).equals(bdz.get(bdz.size() - 1))) {
                bdz.add(bdz.get(0));
            }

            String s2 = BufferPolygonCreator.convertToMercator(bufferPolygon);
            String s3 = BufferPolygonCreator.convertToMercator(bdz);

            // 调用GHB.txt接口
            try {
                AmapSdkCommon.PSRByPolygonRspModel ghbResult = callGHBInterfaceNew(s2);
                String ghbResultJson = objectMapper.writeValueAsString(ghbResult);
                writeFile(deviceFeeder.getPsrId() + "_GHB_result_new", ghbResultJson);
                System.out.println("GHB接口调用成功（新方法），结果已写入文件: " + deviceFeeder.getPsrId() + "_GHB_result_new.json");
            } catch (Exception e) {
                System.err.println("调用GHB接口失败（新方法）: " + e.getMessage());
                e.printStackTrace();
            }

            // 调用bdz.txt接口 (s3作为polygon参数) - 使用新的AmapSdkCommon方法
            try {
                AmapSdkCommon.PSRByPolygonRspModel bdzResult = callBDZInterfaceNew(s3);
                String bdzResultJson = objectMapper.writeValueAsString(bdzResult);
                writeFile(deviceFeeder.getPsrId() + "_BDZ_result_new", bdzResultJson);
                System.out.println("BDZ接口调用成功（新方法），结果已写入文件: " + deviceFeeder.getPsrId() + "_BDZ_result_new.json");
            } catch (Exception e) {
                System.err.println("调用BDZ接口失败（新方法）: " + e.getMessage());
                e.printStackTrace();
            }

         /*   // 将数据直接写到文件中 以馈线名称_GHB 馈线名称_BDZ.json
            String b = objectMapper.writeValueAsString(bufferPolygon);
            String s = objectMapper.writeValueAsString(bdz);
            writeFile(deviceFeeder.getPsrId() + "_BDZ_buffer", s);
            writeFile(deviceFeeder.getPsrId() + "_GHB_buffer", b);
            writeFile(deviceFeeder.getPsrId() + "_GHB", s2);
            writeFile(deviceFeeder.getPsrId() + "_BDZ", s3);*/
        }

        iPlanTestService.handleFeeder();
    }


    /**
     *  使用新的AmapSdkCommon方法
     * @param polygon 多边形参数
     * @return 接口响应结果
     * @throws IOException
     */
    private AmapSdkCommon.PSRByPolygonRspModel callGHBInterfaceNew(String polygon) throws IOException {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("polygon", polygon);
        requestBody.put("srs", "EPSG:3857");

        Map<String, Object> psrQueryInfo = new HashMap<>();
        List<Map<String, Object>> psrQueryList = new ArrayList<>();

        // GHB.txt中的psrQueryList
        Map<String, Object> query1 = new HashMap<>();
        query1.put("psrType", "zf04");
        query1.put("whereClause", "1=1");
        query1.put("attrNameList", null);
        query1.put("distribution", 0);
        psrQueryList.add(query1);

        Map<String, Object> query2 = new HashMap<>();
        query2.put("psrType", "zf07");
        query2.put("whereClause", "1=1");
        query2.put("attrNameList", null);
        query2.put("distribution", 0);
        psrQueryList.add(query2);

        Map<String, Object> query3 = new HashMap<>();
        query3.put("psrType", "wlgt");
        query3.put("whereClause", "1=1");
        query3.put("attrNameList", null);
        query3.put("distribution", 0);
        psrQueryList.add(query3);

        Map<String, Object> query4 = new HashMap<>();
        query4.put("psrType", "0103");
        query4.put("whereClause", "1=1");
        query4.put("attrNameList", null);
        query4.put("distribution", 0);
        psrQueryList.add(query4);

        psrQueryInfo.put("psrQueryList", psrQueryList);

        // 设置attrNameList
        List<String> attrNameList = new ArrayList<>();
        attrNameList.add("classId");
        attrNameList.add("psrId");
        attrNameList.add("psrType");
        attrNameList.add("psrTypeName");
        attrNameList.add("psrName");
        attrNameList.add("zoneId");
        attrNameList.add("ZoneName");
        attrNameList.add("innerTransId");
        attrNameList.add("srcId");
        attrNameList.add("srcName");
        attrNameList.add("portList");
        attrNameList.add("portNameList");
        attrNameList.add("vlevelName");
        attrNameList.add("vlevelCode");
        attrNameList.add("VOLTAGE_LE");
        attrNameList.add("coordinate");
        attrNameList.add("feederId");
        attrNameList.add("feederName");
        attrNameList.add("useNature");
        attrNameList.add("chargedState");
        attrNameList.add("switchStatus");
        attrNameList.add("distribution");
        attrNameList.add("maintCrew");
        attrNameList.add("maintCrewName");
        attrNameList.add("maintOrg");
        attrNameList.add("maintOrgName");
        attrNameList.add("cityOrg");
        attrNameList.add("cityOrgName");
        attrNameList.add("provinceId");
        attrNameList.add("crossFeederId");
        attrNameList.add("crossFeederProvince");
        attrNameList.add("isProvinceContainer");
        attrNameList.add("direction");
        attrNameList.add("dataSource");
        attrNameList.add("plantType");
        attrNameList.add("pubPrivFlag");
        attrNameList.add("siteName");
        attrNameList.add("zoneName");

        psrQueryInfo.put("attrNameList", attrNameList);
        requestBody.put("psrQueryInfo", psrQueryInfo);

        return AmapSdkCommon.queryPSRByPolygon(requestBody);
    }

    /**
     * 使用新的AmapSdkCommon方法
     * @param polygon 多边形参数
     * @return 接口响应结果
     * @throws IOException
     */
    private AmapSdkCommon.PSRByPolygonRspModel callBDZInterfaceNew(String polygon) throws IOException {
        // 构建请求体 - bdz.txt的参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("polygon", polygon);
        requestBody.put("srs", "EPSG:3857");

        Map<String, Object> psrQueryInfo = new HashMap<>();
        List<Map<String, Object>> psrQueryList = new ArrayList<>();

        // bdz.txt中的psrQueryList
        Map<String, Object> query1 = new HashMap<>();
        query1.put("psrType", "zf01");
        query1.put("whereClause", "1=1");
        query1.put("attrNameList", null);
        query1.put("distribution", 1);
        psrQueryList.add(query1);

        Map<String, Object> query2 = new HashMap<>();
        query2.put("psrType", "zf04");
        query2.put("whereClause", "1=1");
        query2.put("attrNameList", null);
        query2.put("distribution", 0);
        psrQueryList.add(query2);

        Map<String, Object> query3 = new HashMap<>();
        query3.put("psrType", "zf07");
        query3.put("whereClause", "1=1");
        query3.put("attrNameList", null);
        query3.put("distribution", 0);
        psrQueryList.add(query3);

        psrQueryInfo.put("psrQueryList", psrQueryList);

        // 设置attrNameList
        List<String> attrNameList = new ArrayList<>();
        attrNameList.add("classId");
        attrNameList.add("psrId");
        attrNameList.add("psrType");
        attrNameList.add("psrTypeName");
        attrNameList.add("psrName");
        attrNameList.add("zoneId");
        attrNameList.add("ZoneName");
        attrNameList.add("innerTransId");
        attrNameList.add("srcId");
        attrNameList.add("srcName");
        attrNameList.add("portList");
        attrNameList.add("portNameList");
        attrNameList.add("vlevelName");
        attrNameList.add("vlevelCode");
        attrNameList.add("VOLTAGE_LE");
        attrNameList.add("coordinate");
        attrNameList.add("feederId");
        attrNameList.add("feederName");
        attrNameList.add("useNature");
        attrNameList.add("chargedState");
        attrNameList.add("switchStatus");
        attrNameList.add("distribution");
        attrNameList.add("maintCrew");
        attrNameList.add("maintCrewName");
        attrNameList.add("maintOrg");
        attrNameList.add("maintOrgName");
        attrNameList.add("cityOrg");
        attrNameList.add("cityOrgName");
        attrNameList.add("provinceId");
        attrNameList.add("crossFeederId");
        attrNameList.add("crossFeederProvince");
        attrNameList.add("isProvinceContainer");
        attrNameList.add("direction");
        attrNameList.add("dataSource");
        attrNameList.add("plantType");
        attrNameList.add("pubPrivFlag");
        attrNameList.add("siteName");
        attrNameList.add("zoneName");

        psrQueryInfo.put("attrNameList", attrNameList);
        requestBody.put("psrQueryInfo", psrQueryInfo);

        return AmapSdkCommon.queryPSRByPolygon(requestBody);
    }

    /**
     * 写入文件
     * @param fileName
     * @param content
     * @throws Exception
     */
    private void writeFile(String fileName, String content) throws Exception {
        Files.write(Paths.get("C:\\Users\\<USER>\\Desktop\\temp\\data\\" + fileName + ".json"), content.getBytes());
        System.out.println("写入文件成功");
    }

}
