package com.ruoyi.entity.map.bo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;

/**
 * 附近变电站查询请求对象
 * 根据坐标点查询附近变电站信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class NearbySubstationQueryBo {

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度必须在-180到180之间")
    private Double lng;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间")
    private Double lat;

    @NotNull(message = "纬度不能为空")
    private String point;
    /**
     * 查询半径（米）
     */
    @NotNull(message = "查询半径不能为空")
    @Min(value = 1, message = "查询半径必须大于0")
    private Double bufferRadius;


    public Double getBufferRadius() {
        return bufferRadius * 1000;
    }
}
