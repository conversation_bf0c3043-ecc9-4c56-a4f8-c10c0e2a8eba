package com.ruoyi.service.plan.model.plan;

import ch.qos.logback.core.util.StringCollectionUtil;
import com.ruoyi.graph.utils.CartesianProduct;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 方案备选lay操作组合
 * 用于生成方案主要实体类
 */
@Data
public class CombAlternateOp {

    /**
     * 备选方案操作lay
     */
    List<AlternateOp> alternateOps = new ArrayList<>();

    /**
     * 新增
     */
    public void add(AlternateOp alternateOp) {
        alternateOps.add(alternateOp);
    }

    /**
     * 新增
     */
    public void addAll(List<AlternateOp> alternateOps) {
        this.alternateOps.addAll(alternateOps);
    }

    /**
     * 将备选方案组合转为二维数组方案操作形式
     */
    public List<List<PlanOperate>> toPlanOps() {
        List<List<PlanOperate>> result = new ArrayList<>();
        for (AlternateOp alternateOp : alternateOps) {
            result.add(alternateOp.getAlternateOps());
        }
        return result;
    }

    /**
     * 过滤空
     */
    public void filterEmpty() {
        for (AlternateOp alternateOp : alternateOps) {
            alternateOp.filterEmpty();
        }
        alternateOps = alternateOps.stream().filter(n -> !n.isEmpty()).collect(Collectors.toList());
    }

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(alternateOps);
    }

    /**
     * 转为确定的方案
     * 把备选里面所有的分别组合组成确定的
     */
    public SurePlanOp toSurePlanOp() {
        SurePlanOp surePlanOp = new SurePlanOp();

        // 数据扁平放在同一个planOp里面
        List<List<PlanOperate>> planOpList = toPlanOps();

        // 去空
        planOpList = planOpList.stream().filter(ns -> !ns.isEmpty()).collect(Collectors.toList());

        // 备用数值里面  都需要组合在一起
        List<List<PlanOperate>> mergePlanOps = CartesianProduct.cartesianProduct(planOpList);

        for (List<PlanOperate> planOps : mergePlanOps) {
            // 合并
            PlanOperate planOperate = PlanOperateFactory.mergePlanOps(planOps);
            if (planOperate != null) {
                surePlanOp.add(planOperate);
            }
        }

        return surePlanOp;
    }

    /**
     * 判断是否存在某个planOp的类型
     */
    public boolean hasPlanOpType(String type) {
        List<PlanOperate> allPlanOp = getAllPlanOp();
        return allPlanOp.stream().anyMatch(op -> StringUtils.equals(op.getType(), type));
    }

    /**
     * 获取所有的planOp
     */
    public List<PlanOperate> getAllPlanOp() {
        List<PlanOperate> result = new ArrayList<>();
        for (AlternateOp alternateOp : getAlternateOps()) {
            result.addAll(alternateOp.getAlternateOps());
        }
        return result;
    }

    /**
     * 获取当前备选组合所有的lay
     */
    public List<BaseLay> getAllLayNode() {
        List<BaseLay> result = new ArrayList<>();
        for (AlternateOp alternateOp : getAlternateOps()) {
            for (PlanOperate op : alternateOp.getAlternateOps()) {
                result.addAll(op.getLayNodeList());
            }
        }
        return result;
    }
}
