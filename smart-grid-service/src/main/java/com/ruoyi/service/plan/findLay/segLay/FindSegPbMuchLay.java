package com.ruoyi.service.plan.findLay.segLay;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.findLay.NodeNumModel;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.SegPbMuchPlan.SegLayPathBo;
import com.ruoyi.service.plan.utils.LayNodeFactory;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 主干路径就放置分段开关或者联络线工具类
 */
public class FindSegPbMuchLay {

    /**
     * 获取主干路径上放置分段开关位置集合
     */
    public static CombAlternateOp getLayPositionList(List<SegLayPathBo> mainPathsBos, HashMap<String, BranchNode> branchNodeMap, int maxNum, List<Node> excludeEdges) {

        // 过滤只有主干路就放置分段开关的
        List<SegLayPathBo> segLayPathBos = mainPathsBos.stream().filter(SegLayPathBo::isSegPath).collect(Collectors.toList());

        // 没有直接返回
        if (CollectionUtils.isEmpty(segLayPathBos)) {
            return new CombAlternateOp();
        }

        // 2、 模型简单化
        List<ArrayList<NodeNumModel>> singleModelPaths = toSingleModelPaths(mainPathsBos, branchNodeMap, excludeEdges);

        // 3、查找分割开关组合
        List<List<List<Integer>>> kgCombs = singleModelPaths.stream().map(singleModelPath -> findKgCombination(singleModelPath, maxNum)).collect(Collectors.toList());

        // 4、最小组合
        List<List<List<Integer>>> minKgCombs = kgCombs.stream().map(ListUtils::extractMinLengthLists).collect(Collectors.toList());

        return getLayPositionList(minKgCombs, mainPathsBos, singleModelPaths);
    }

    /**
     * 模型简单化
     * 将每个路径的分叉节点  都使用当前分叉节点分支路径下 的配变数量表示
     */
    private static List<ArrayList<NodeNumModel>> toSingleModelPaths(List<SegLayPathBo> mainPathsBos, HashMap<String, BranchNode> branchNodeMap, List<Node> excludeEdges) {

        List<ArrayList<NodeNumModel>> result = new ArrayList<>();
        if (org.springframework.util.CollectionUtils.isEmpty(mainPathsBos)) {
            return result;
        }

        for (int oneIndex = 0; oneIndex < mainPathsBos.size(); oneIndex++) {
            SegLayPathBo segMainPathBo = mainPathsBos.get(oneIndex);

            ArrayList<Node> nodePath = segMainPathBo.getNodes();
            // 其它假主干
            List<ArrayList<Node>> otherFakeNodePaths = new ArrayList<>();
            for (int i = 0; i < mainPathsBos.size(); i++) {
                if (i != oneIndex) {
                    otherFakeNodePaths.add(mainPathsBos.get(i).getNodes());
                }
            }

            ArrayList<NodeNumModel> modelPaths = new ArrayList<>();
            for (int i = 1; i < nodePath.size(); i++) {
                Node node = nodePath.get(i);
                if (node.isPole()) {
                    List<Node> edges = node.getEdges();
                    List<Node> otherEdges = new ArrayList<>();

                    // 排除当前线上的和其它的线
                    for (Node edge : edges) {
                        // 排除主干线
                        if (nodePath.contains(edge)) {
                            continue;
                        }

                        if (NodeUtils.hasNode(otherFakeNodePaths, edge)) {
                            continue;
                        }

                        if (excludeEdges != null && excludeEdges.contains(edge)) {
                            continue;
                        }
                        otherEdges.add(edge);
                    }

                    // 排除其它
                    if (!otherEdges.isEmpty()) {

                        // 计算所有分支路径下的配变总和
                        int sum = 0;
                        // 获取当前其它陪伴总和
                        for (Node edge : otherEdges) {
                            String branchId = BranchNode.processId(node, edge);
                            BranchNode branchNode = branchNodeMap.get(branchId);
                            if (branchNode != null) {
                                sum += branchNode.getPbNodeNum();
                            }
                        }

                        // 排除其它的
                        if (sum > 0) {
                            NodeNumModel nodeNumModel = new NodeNumModel(sum, node, i < nodePath.size() - 1 ? nodePath.get(i + 1) : null);
                            modelPaths.add(nodeNumModel);
                        }
                    }

                }
            }
            result.add(modelPaths);
        }

        return result;
    }


    /**
     * 查找放置单条路径可行的分段开关的组合
     * 例如：[
     * [0,1,3],
     * [2,4]
     * ]
     */
    public static List<List<Integer>> findKgCombination(List<NodeNumModel> modelPaths, int max) {
        List<List<Integer>> result = new ArrayList<>();
        int end = modelPaths.size();

        // 1、预处理前缀和数组（prefix[i] = 前i个分支的和）
        int[] prefix = new int[end + 1];
        for (int i = 0; i < end; i++) {
            prefix[i + 1] = prefix[i] + modelPaths.get(i).getNum();
        }

        // 2、回溯法生成所有开关分割组合
        NodeUtils.backtrack(0, end, prefix, new ArrayList(), result, max);
        return result;
    }

    /**
     * 获取所以路径放在位置的所有组合
     */
    private static CombAlternateOp getLayPositionList(List<List<List<Integer>>> minKgCombs,
                                                      List<SegLayPathBo> mainPathsBos,
                                                      List<ArrayList<NodeNumModel>> singleModelPaths) {
        CombAlternateOp combAlternateOp = new CombAlternateOp();

        // 遍历多条路径的所有组合
        for (int i = 0; i < minKgCombs.size(); i++) {
            // 单条路径的所有组合
            List<List<Integer>> combPositions = minKgCombs.get(i);
            // 当前当条路径的路径
            ArrayList<Node> pathNodes = mainPathsBos.get(i).getNodes();
            ArrayList<NodeNumModel> numModels = singleModelPaths.get(i);

            AlternateOp alternateOp = getLayPlanOp(combPositions, pathNodes, numModels);

            combAlternateOp.add(alternateOp);
        }
        return combAlternateOp;
    }

    /**
     * 获取所以路径放在位置的所有组合
     */
    public static AlternateOp getLayPlanOp(List<List<Integer>> combPositions, ArrayList<Node> pathNodes, ArrayList<NodeNumModel> numModels) {

        AlternateOp alternateOp = new AlternateOp();

        // 遍历所有组合
        for (int j = 0; j < combPositions.size(); j++) {
            // 当前组合
            List<Integer> positions = combPositions.get(j);
            // 表示首位
            if (positions.isEmpty() || (positions.size() == 1 && positions.get(0) == 0)) {
                continue;
            }

            alternateOp.add(getLayPosition(pathNodes, numModels, positions));
        }
        return alternateOp;
    }

    /**
     * 获取路径上 放着开关或者联络线的 对象
     *
     * @param pathNodes    路径节点集合
     * @param models       对应的数字模型
     * @param positionList 放在第位置下标集合
     * @return
     */
    private static PlanOperate getLayPosition(ArrayList<Node> pathNodes, ArrayList<NodeNumModel> models, List<Integer> positionList) {

        List<Integer> positions = new ArrayList<>(positionList);
        // 从小到小排序
        positions.sort(Comparator.comparingInt(a -> a));

        List<BaseLay> layNodes = new ArrayList<>();

        // 大分子放置首开关
        for (int i = 0; i < positions.size(); i++) {
            int position = positions.get(i) - 1;
            if (position < 0) {
                continue;
            }

            // 开始和结束的杆塔
            Node startNode = models.get(position).getNode();
            Node endNode = position + 1 < models.size() ? models.get(position + 1).getNode() : null;

            BaseLay layNode = getLayNodeBo(pathNodes, startNode, endNode);
            if (layNode != null) {
                layNodes.add(layNode);
            }
        }

        return PlanOperateFactory.createSegKg(layNodes);
    }

    private static BaseLay getLayNodeBo(List<Node> pathNodes, Node startNode, Node endNode) {
        List<Node> range = null;
        // 开始查找开始到结束可以放置区间的节点
        for (Node node : pathNodes) {
            // 表示
            if (node.equals(startNode)) {
                range = new ArrayList<>();
            }
            if (endNode != null && node.equals(endNode)) {
                break;
            }
            if (range != null) {
                range.add(node);
            }
        }

        if (CollectionUtils.isEmpty(range)) {
            return null;
        }

        // 获取当前出现开关的首位置下标
        Node firstKg = ListUtils.findFirst(range, n -> n.isKg("all"));
        if (firstKg == null) {
            Node middlePole = NodeUtils.getMiddlePole(range);
            for (int i = 0; i < range.size(); i++) {
                Node node = range.get(i);
                if (node.equals(middlePole)) {
                    return LayNodeFactory.createKg(range.get(i), range.get(i + 1), null);
                }
            }
            return null;
        } else {
            // 如果这段区间已经有开关了  那么就不需要上新了
            return LayNodeFactory.createKg(firstKg, null);
        }
    }
}
