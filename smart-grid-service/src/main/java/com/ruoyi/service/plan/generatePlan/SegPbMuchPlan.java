package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.findLay.contactLay.FindEndContactLay;
import com.ruoyi.service.plan.findLay.contactLay.utils.NumModelUtil;
import com.ruoyi.service.plan.model.ContactLayConfig;
import com.ruoyi.service.plan.model.SegPbMuchPlan.SegLayPathBo;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.findLay.NodeNumModel;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.problem.ProblemRuleMapper;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.findLay.segLay.FindSegPbMuchLay;
import com.ruoyi.service.znap.IZnapTopologyService;
import com.ruoyi.util.ListUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 生成分段内配变数量不合理方案
 */
@Component
@Slf4j
public class SegPbMuchPlan implements IGeneratePlan {

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    ProblemRuleMapper problemRuleMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    FindEndContactLay findEndContactLay;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) throws ExecutionException, InterruptedException {

        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        List<Node> kgContactNodes = nodePath.getContactKgNodes();

        // 主干分段对象
        SegBetween segBetween = segBranchService.getMainSegBetween(nodePath, deviceId, kgContactNodes);

        if (segBetween == null) {
            throw new RuntimeException("暂未获取当前的分段，请确定数据正确性！");
        }

        // 分支对象
        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();
        List<Node> betweenNodes = segBetween.getBetweenNodes(true);
        List<Node> allPb = segBetween.getAllPb();

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // 最大限制
        int maxNums = iConditionService.unreasonablePBNum(deviceFeeder.getSupplyArea());

        // (3)、推送：问题释义
        pushPlanProcessService.pushExplain(problemId, maxNums);

        // (4)、推送：分段识别
        pushPlanProcessService.pushSegIdentify(problemId, segBetween);

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        SurePlanOp surePlanOp = new SurePlanOp();

        // 主干路径
        List<SegLayPathBo> mainPathsBos = getSegLayPaths(betweenNodes, branchNodeMap, maxNums);

        // =========================== 分段开关 =======================
        CombAlternateOp segKgCombOp = getSegKgCombPlanOp(mainPathsBos, maxNums, nodePath);
        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(segKgCombOp);

        processNodeService.handleLayOperateNode(surePlanOp, problemId, segKgCombOp, token, feederId, nodePath);

        // =========================== 临近线路新上联络 =======================
        CombAlternateOp contactCombOp = getContactCombPlanOp(mainPathsBos, maxNums, nodePath, PlanOperate.CONTACT_TYPE);

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(contactCombOp);

        processNodeService.handleLayOperateNode(surePlanOp, problemId, contactCombOp, token, feederId, nodePath);

        // =========================== 变电站新出线 =======================
        CombAlternateOp bdzCombOp = getContactCombPlanOp(mainPathsBos, maxNums, nodePath, PlanOperate.BDZ_NEW_LINE_TYPE);

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(bdzCombOp);

        processNodeService.handleLayOperateNode(surePlanOp, problemId, bdzCombOp, token, feederId, nodePath);

        // =========================== 方案生成 =======================

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, plans, planLaysMap);

        return plans;
    }


    /**
     * 获取需要递归查找分段的路径
     * 按道理当前主干路径即可，但是 有一种特殊的线路 当前主干线路的大分子配变数量超过最大限定值，那么该大分子需要末端新增联络线
     */
    private List<SegLayPathBo> getSegLayPaths(List<Node> betweenNodes, HashMap<String, BranchNode> branchNodeMap, int maxNums) {

        List<SegLayPathBo> results = new ArrayList<>();

        ArrayList<Node> nodes = new ArrayList<>();

        for (int i = 0; i < betweenNodes.size(); i++) {
            Node node = betweenNodes.get(i);
            List<Node> edges = node.isEdge() ? null : node.getEdges();
            nodes.add(node);
            // 杆塔
            if (node.isPole()) {
                // 杆塔上可能会有分支线 && i < betweenNodes.size() - 1
                if (edges != null && edges.size() > 2) {
                    // 过滤已经主干边 其它的分叉边
                    List<Node> branchEdges = edges.stream().filter(n -> !betweenNodes.contains(n)).collect(Collectors.toList());
                    List<NodeNumModel> nodeNums = NumModelUtil.toPbNums(branchEdges, node, branchNodeMap);
                    for (NodeNumModel nodeNum : nodeNums) {
                        // 单个分支超过最大数量
                        if (NumModelUtil.judgePbNumModel(nodeNum, maxNums)) {
                            results.add(SegLayPathBo.createBranchPath(nodeNum.getNode(), nodeNum.getNextNode()));
                        }
                    }
                }
            }
        }

        results.add(SegLayPathBo.createSegPath(nodes));
        return results;
    }

    private CombAlternateOp getSegKgCombPlanOp(List<SegLayPathBo> mainPathsBos, int maxNum, NodePath nodePath) {
        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();

        // 主干路路径放在分段开关或者联络线
        List<SegLayPathBo> mainSegPathLays = mainPathsBos.stream().filter(SegLayPathBo::isSegPath).collect(Collectors.toList());

        // 大分子路径
        List<SegLayPathBo> branchPathLays = mainPathsBos.stream().filter(n -> !n.isSegPath()).collect(Collectors.toList());

        // 分支线的开始边集合
        List<Node> branchStartEdges = new ArrayList<>();
        for (SegLayPathBo branchPathLay : branchPathLays) {
            Node branchNextNode = branchPathLay.getBranchNextNode();
            if (branchNextNode != null) {
                branchStartEdges.add(branchNextNode);
            }
        }

        return FindSegPbMuchLay.getLayPositionList(mainSegPathLays, branchNodeMap, maxNum, branchStartEdges);
    }

    /**
     * 获取放置节点集合
     */
    private CombAlternateOp getContactCombPlanOp(List<SegLayPathBo> mainPathsBos, int maxNum, NodePath nodePath, String type) {
        CombAlternateOp result = new CombAlternateOp();

        // 大分子路径
        List<SegLayPathBo> branchPathLays = mainPathsBos.stream().filter(n -> !n.isSegPath()).collect(Collectors.toList());

        for (SegLayPathBo branchPathLay : branchPathLays) {
            Node startNode = branchPathLay.getBranchStartNode();
            Node nextEdge = branchPathLay.getBranchNextNode();

            List<ContactBranch> bigBranchs = findEndContactLay.getPbNumBranch(startNode, nextEdge, maxNum, nodePath);

            CombAlternateOp combAleOp = ContactLayPlanOp.getBigBranchCombAltOp(bigBranchs, nodePath, type, new ContactLayConfig(true, null, true));
            List<AlternateOp> alternateOps = combAleOp.getAlternateOps();

            for (int i = 0; i < alternateOps.size(); i++) {
                AlternateOp alternateOp = alternateOps.get(i);
                ContactBranch contactBranch = bigBranchs.get(i);
                List<BranchNode> extraBranchNodes = contactBranch.getExtraBranchNodes();
                List<PlanOperate> newPlanOps = new ArrayList<>();

                // 联络点也是有讲究 并不是每个路径末端联络点都还是能保持分段数量 所以需要过滤
                for (PlanOperate op : alternateOp.getAlternateOps()) {
                    List<ContactLay> contactLays = op.getContactLays();
                    if (contactLays.isEmpty()) {
                        newPlanOps.add(op);
                    } else {
                        // 需要判断设置 前面已经拆分各个满足的分支  所以不会出现多个  只会有一个联络点
                        ContactLay contactLay = contactLays.get(0);
                        // 当前的路径 我们要从这里遍历判断当前主干路近是否满足
                        List<Node> subPath = contactLay.getProcessContactBo().getSubPath();
                        SegCanPath segCanPath = judgeSegCanPath(subPath, nodePath, maxNum, extraBranchNodes);
                        if (segCanPath.can) {
                            op.addAll(segCanPath.getAddSegKgLay());
                            newPlanOps.add(op);
                        }
                    }
                }
                // 重新设置
                alternateOp.setAlternateOps(newPlanOps);
            }

            result.addAll(combAleOp.getAlternateOps());
        }
        result.setAlternateOps(result.getAlternateOps().stream().filter(n -> !n.isEmpty()).collect(Collectors.toList()));
        return result;
    }

    SegCanPath judgeSegCanPath(List<Node> subPath, NodePath nodePath, int maxNum, List<BranchNode> extraBranchNodes) {
        boolean can = true;
        List<BaseLay> addSegKgLay = new ArrayList<>();

        // 这条路径是否满足
        List<String> extraEdgeIds = extraBranchNodes.stream().map(BranchNode::getNextId).collect(Collectors.toList());

        int beforePbTotal = 0;
        int beforeKgIndex = 0;
        List<NodeNumModel> beforeNumModels = new ArrayList<>();
        for (int i = 1; i < subPath.size() - 1; i++) {
            Node currentNode = subPath.get(i);
            Node nextNode = subPath.get(i + 1);
            Node beforeNode = subPath.get(i - 1);

            int pbTotal = 0;

            List<Node> edges = currentNode.getEdges();
            // 1、处理是否满足
            if (CollectionUtils.isNotEmpty(edges) && edges.size() >= 3) {

                List<String> filterIds = new ArrayList<>(extraEdgeIds);
                filterIds.add(nextNode.getId());
                filterIds.add(beforeNode.getId());

                // 过滤过滤路径前后边  （只需判断排除当前路径上的其他边）
                edges = edges.stream().filter(n ->
                        filterIds.stream().noneMatch(id -> StringUtils.equals(id, n.getId()))
                ).collect(Collectors.toList());

                if (!edges.isEmpty()) {
                    List<NodeNumModel> pbNums = NumModelUtil.toPbNums(edges, currentNode, nodePath.getBranchAllNodeMap());

                    pbTotal = pbNums.stream().mapToInt(NodeNumModel::getNum).sum();

                    beforeNumModels.addAll(pbNums);

                    // 查找不满足的
                    boolean meet = pbNums.stream().anyMatch(numModel -> NumModelUtil.judgePbNumModel(numModel, maxNum));
                    // 只有有一个不满足的那么这条路径也就不满足了
                    if (meet) {
                        can = false;
                        break;
                    }
                }
            }

            beforePbTotal += pbTotal;

            // 2、处理是否补充分段开关
            if (currentNode.isKg("all") || i == subPath.size() - 1) {

                // 这区间上分段开关
                if (beforePbTotal >= maxNum) {
                    // 3、查找分割开关组合
                    List<List<Integer>> kgCombs = FindSegPbMuchLay.findKgCombination(beforeNumModels, maxNum);
                    List<List<Integer>> minKgCombs = ListUtils.extractMinLengthLists(kgCombs);
                    List<Node> path = subPath.subList(beforeKgIndex, i + 1);

                    AlternateOp layPlanOp = FindSegPbMuchLay.getLayPlanOp(
                            minKgCombs,
                            new ArrayList<>(path),
                            new ArrayList<>(beforeNumModels));
                    List<BaseLay> allLay = layPlanOp.getAllLay();
                    if (!allLay.isEmpty()) {
                        addSegKgLay.add(allLay.get(0));
                    }
                }

                // 遇到开关 清零当前分段配变数量
                beforePbTotal = 0;
                beforeKgIndex = i;
                beforeNumModels.clear();
            }
        }

        return new SegCanPath(can, addSegKgLay);

    }

    @Data
    private class SegCanPath {
        public SegCanPath(boolean can, List<BaseLay> addSegKgLay) {
            this.can = can;
            this.addSegKgLay = addSegKgLay;
        }

        private boolean can;

        private List<BaseLay> addSegKgLay;
    }

}
