package com.ruoyi.service.plan.impl.test;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.constant.SegmentationEnum;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.mapper.znap.ConDmsFeederMapper;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.generatePlan.SegPbMuchPlan;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.*;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.bo.SingMapFigure;
import com.ruoyi.mapper.device.*;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.plan.IPlanTestService;
import com.ruoyi.service.plan.generatePlan.SegPbMuchPlan;
import com.ruoyi.service.znap.IZnapTopologyService;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;


@Service
public class PlanTestServiceImpl implements IPlanTestService {

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    DeviceSubstation2Mapper deviceSubstation2Mapper;

    @Autowired
    DeviceStationBreakerMapper deviceStationBreakerMapper;

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    DeviceFeederCableMapper deviceFeederCableMapper;

    @Autowired
    DeviceFeederJkMapper deviceFeederJkMapper;

    @Autowired
    DeviceCableTerminalJointMapper deviceCableTerminalJointMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    private ProblemMapper problemMapper;

    @Autowired
    DeviceWlgtMapper deviceWlgtMapper;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    ConDmsFeederMapper conDmsFeederMapper;

    /**
     * 单线图分析
     */
    SingMapFigureAnalysis singMapFigureAnalysis;

    @Autowired
    SegPbMuchPlan segPgMuchPlan;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    ContactHandleService contactHandleService;


    // 92f6f972-deeb-414a-892c-42c315867b76
    public void text(String feederId) {
        DeviceFeeder feeder = feederDeviceMapper.selectById(feederId);

        // 变电站
        DeviceSubstation substation = deviceSubstation2Mapper.selectById(feeder.getStartStation());

        try {

            // 单线图figure分析相关
            singMapFigureAnalysis = new SingMapFigureAnalysis(feederId);

            // 节点集合
            Map<String, Node> nodeMap = singMapFigureAnalysis.toNodeMap();

            ArrayList<Node> nodes = new ArrayList<>(nodeMap.values());

            SingMapFigure bdzFigure = singMapFigureAnalysis.getByPsrId(substation.getPsrId(), false);

            // 变电站内断路器(0305) // 根据线路 查不出所有
            List<DeviceStationBreaker> feederStationBreakers = deviceStationBreakerMapper.selectByFeederId(feederId);

            // 站内断路器-》

            // 配电电缆终端接头（0202）
            List<DeviceCableTerminalJoint> cableTerminalJoints = deviceCableTerminalJointMapper.selectByFeederId(feederId);

            // 输电运行杆塔(0103)
            List<DeviceRunTower> runTowers = deviceRunTowerMapper.selectByFeederId(feederId);

            // 架空线
            List<DeviceFeederJk> feederJks = deviceFeederJkMapper.selectByFeederId(feederId);

            // 电缆线段
            List<DeviceFeederCable> feederCables = deviceFeederCableMapper.selectByFeederId(feederId);

            //
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public ArrayList<SegBetween> getSegBetweenList(String feederId, String deviceId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        // BranchNode mainBranch = segBranchService.getMainBranch(nodePath, deviceId);

//        Node station = nodeMap.get(deviceId);
//        if (station != null) {
//            List<Node> bayNodes = NodeUtils.getSurplusBayEndNode(station);
//            System.out.println(bayNodes);
//        }

        return nodePath.getSegBetweenList();
    }

    @Override
    public ArrayList<Node> getContactKgs(String feederId) {

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        ArrayList<Node> kgContactNodes = (ArrayList<Node>) nodePath.getContactKgNodes();

        return kgContactNodes;
    }

    @Override
    public void handleFeeder() {
        getSegBetweenList("01DKX-8853", "");
        //      List<ConDmsFeeder> conDmsFeeders = conDmsFeederMapper.selectList();
//        for (ConDmsFeeder conDmsFeeder : conDmsFeeders) {
//            String psrid = conDmsFeeder.getPsrid();
//
//            String feederId = ZnapUtils.parsePsrId(psrid);
//            ZnapTopology znapTopology = null;
//
//            Node startNode = znapTopology.getStartNode();
//            Map<String, Node> nodeMap = znapTopology.getNodeMap();
//            ArrayList<Node> kgContactNodes = (ArrayList<Node>) znapTopology.getKgContactNodes();
//            Map<String, ArrayList<Node>> paths = znapTopology.getPaths();
//            ArrayList<Node> nodeList = znapTopology.getNodeList();
//            System.out.println("线路ID" + feederId);
//            if (CollectionUtils.isEmpty(kgContactNodes)) {
//                continue;
//            }
//            NodePath nodePath = new NodePath();
//
//            nodePath.analysisPath(startNode, kgContactNodes);
//
//            ArrayList<SegBetween> segBetweenList = nodePath.getSegBetweenList();
//            if (!segBetweenList.isEmpty()) {
//                System.out.println(nodePath);
//            }
//        }

    }

    /**
     * 获取联络开关对应各个主干路径都主干开关的各个分段
     */
    @Override
    public List<HashMap<String, Object>> getAllContactKgSegList(String feederId) {

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();

        // 所有的联络开关
        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();
        List<HashMap<String, Object>> result = new ArrayList<>();

        for (ContactFeederKg contactFeederKg : contactFeederKgs) {
//            List<SegBetween> contactKgAllSeg = nodePath.getContactKgAllSeg(contactFeederKg.getKgPsrId());
//            if (CollectionUtils.isNotEmpty(contactKgAllSeg)) {
//                HashMap<String, Object> data = new HashMap<>();
//                data.put("psrId", contactFeederKg.getKgPsrId());
//                data.put("psrType", contactFeederKg.getKgPsrType());
//                data.put("psrName", contactFeederKg.getKgPsrName());
//
//                data.put("feederPsrId", contactFeederKg.getFeederPsrId());
//                data.put("feederPsrName", contactFeederKg.getFeederPsrName());
//
//                data.put("segList", NodeUtils.toSegBetweenVoList(contactKgAllSeg));
//                result.add(data);
//            }

            //
            for (ArrayList<Node> mainNodePath : nodePath.getMainNodePaths()) {
                Node contactNode = null;
                Node nextNode = null;
                int contactIndex = -1;
                for (int i = mainNodePath.size() - 1; i >= 0; i--) {
                    // 主干路径的开关 需要和联络开关做分段
                    if (StringUtils.equals(mainNodePath.get(i).getPsrId(), contactFeederKg.getKgPsrId())) {
                        contactNode = mainNodePath.get(i);
                        nextNode = mainNodePath.get(i - 1);
                        contactIndex = i;
                    }
                }
                if (contactNode != null) {
                    HashMap<String, Object> data = new HashMap<>();
                    data.put("psrId", contactFeederKg.getKgPsrId());
                    data.put("psrType", contactFeederKg.getKgPsrType());
                    data.put("psrName", contactFeederKg.getKgPsrName());
                    data.put("feederPsrId", contactFeederKg.getFeederPsrId());
                    data.put("feederPsrName", contactFeederKg.getFeederPsrName());

                    ArrayList<SegBetweenVo> segList = new ArrayList<>();

                    for (int i = contactIndex - 1; i >= 0; i--) {
                        Node fenNode = mainNodePath.get(i);
                        // 主干路径的开关 需要和联络开关做分段
                        if (fenNode.isKg("all")) {
                            List<Node> nodes = contactHandleService.powerSupplyNodes(contactNode, nextNode, fenNode);
                            String segId = SegBetween.getSegId(fenNode.getPsrId(), contactFeederKg.getKgPsrId());
                            SegBetweenVo segBetweenVo = new SegBetweenVo(segId, fenNode.getPsrId(), fenNode.getPsrType(), fenNode.getPsrName(), contactFeederKg.getKgPsrId(), contactFeederKg.getKgPsrType(), contactFeederKg.getKgPsrName());
                            segBetweenVo.setNodes(NodeUtils.toNodeVos(nodes));
                            segList.add(segBetweenVo);
                        }
                    }
                    data.put("segList", segList);
                    result.add(data);
                    break;
                }

            }
        }

        return result;
    }

    @Override
    public List<Node> getMainPath(String feederId) {

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        ArrayList<Node> result = new ArrayList<>();
        for (ArrayList<Node> mainNodePath : nodePath.getMainNodePaths()) {
            result.addAll(mainNodePath);
        }

        return ListUtils.distinctByKey(result, Node::getId);
    }
}
