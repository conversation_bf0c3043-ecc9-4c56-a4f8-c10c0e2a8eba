package com.ruoyi.service.plan.model;

import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新增联络线 以下如果条件不足 逐个往下走
 * 1、从杆塔出发新增联络线
 * 2、末端是有站房的设备 并且站房有多余的间隔 从备用间隔
 * 3、末端有站房如果站房站房当前使用的间隔低于6个 那么我们可以替换大点的间隔站房  否则 新增新的站房
 * 4、末端没有站房 新增新的环网柜
 */
@Data
public class ProcessContactBo {

    public ProcessContactBo(String type, List<Node> belongPath) {
        this.type = type;
        this.belongPath = belongPath;
    }

    String type;

//    /**
//     * 当前所属的方案planOperate的联络类型：临近联络线 或者 变电站新出线
//     * 具体参考 PlanOperate.type
//     */
//    String contactType;

    /**
     * POLE_RANGE关联: 需要设置联络线的杆塔
     */
    Node pole;

    /**
     * STATION_BAY关联：放在的间隔节点
     */
    HwgBayBo hwgBay;

    /**
     * SEG_ADD_HWG关联：打断导线段对象
     */
    SegBreakNodeBo segBreakNode;

    /**
     * STATION_BAY关联：替换站房
     */
    ReplaceStationNodeBo replaceStationNode;

    /**
     * 关联LayNodeBo的ID
     */
    String layNodeId;

    /**
     * 所属的路径 从那个路径开始到结尾查找的路径
     */
    List<Node> belongPath;

    // 杆塔新增联络线
    public static String POLE_RANGE = "poleRange";

    // 末端是有站房 并且站房有多余的间隔 从备用间隔
    public static String STATION_BAY = "stationBay";

    // 新增新的环网柜
    public static String SEG_ADD_HWG = "setAddHwg";

    // 站房替换
    public static String HWG_REPLACE = "hwgReplace";

    public static HashMap<String, Integer> scopeByType = new HashMap<String, Integer>() {{
        put(POLE_RANGE, 30);
        put(STATION_BAY, 40);
        put(SEG_ADD_HWG, 20);
        put(HWG_REPLACE, 10);
    }};

    /**
     * 杆塔新增联络线
     */
    public static ProcessContactBo createPoles(Node pole, List<Node> belongPath) {
        ProcessContactBo processContactBo = new ProcessContactBo(POLE_RANGE, belongPath);
        processContactBo.setPole(pole);
        return processContactBo;
    }

    /**
     * 从备用间隔新出现
     */
    public static ProcessContactBo createHwgBay(HwgBayBo hwgBay, List<Node> belongPath) {
        ProcessContactBo processContactBo = new ProcessContactBo(STATION_BAY, belongPath);
        processContactBo.setHwgBay(hwgBay);
        return processContactBo;
    }

    /**
     * 新增新的环网柜
     */
    public static ProcessContactBo createSegBreakNode(SegBreakNodeBo segBreakNode, List<Node> belongPath) {
        ProcessContactBo processContactBo = new ProcessContactBo(SEG_ADD_HWG, belongPath);
        processContactBo.setSegBreakNode(segBreakNode);
        return processContactBo;
    }

    /**
     * 站房替换
     */
    public static ProcessContactBo createStationReplace(ReplaceStationNodeBo replaceStationNode, List<Node> belongPath) {
        ProcessContactBo processContactBo = new ProcessContactBo(HWG_REPLACE, belongPath);
        processContactBo.setReplaceStationNode(replaceStationNode);
        return processContactBo;
    }

    public int getScopeByType() {
        return scopeByType.get(type);
    }

    /**
     * 获取当前开始到当前联络点的路径
     */
    public List<Node> getSubPath() {
        Node endNode = null;
        // 确定当前路径的点
        if (StringUtils.equals(type, POLE_RANGE)) {
            endNode = pole;
        } else if (StringUtils.equals(type, STATION_BAY)) {
            List<Node> bayNodes = hwgBay.getBayNodes();
            // TODO 按道理需要出现开关  后面优化  目前这里无关紧要
            if (!bayNodes.isEmpty()) {
                endNode = bayNodes.get(0);
            }
        }
        if (StringUtils.equals(type, SEG_ADD_HWG)) {
            endNode = segBreakNode.getStartNode();
        }
        return NodeUtils.subLiceNode(belongPath, null, endNode);
    }

}
