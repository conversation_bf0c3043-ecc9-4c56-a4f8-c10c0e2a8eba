package com.ruoyi.service.plan.model.plan;

import com.ruoyi.service.plan.model.lay.BaseLay;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 备选操作集合
 * 储存备选方案操作数组 组合为正式方案时 会使用里面其中一个方案操作
 */
@Data
public class AlternateOp {

    public AlternateOp() {
    }

    public AlternateOp(List<PlanOperate> alternateOps) {
        this.alternateOps = alternateOps;
    }

    public AlternateOp(PlanOperate alternateOp) {
        this.alternateOps = Collections.singletonList(alternateOp);
    }

    List<PlanOperate> alternateOps = new ArrayList<>();

    /**
     * 新增
     */
    public void add(PlanOperate planOp) {
        alternateOps.add(planOp);
    }

    /**
     * 新增
     */
    public void addAll(List<PlanOperate> planOps) {
        alternateOps.addAll(planOps);
    }

    /**
     * 过滤空
     */
    public void filterEmpty() {
        for (PlanOperate alternateOp : alternateOps) {
            alternateOp.filterEmpty();
        }

        alternateOps = alternateOps.stream().filter(n -> !n.isEmpty()).collect(Collectors.toList());
    }

    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(alternateOps);
    }

    /**
     * 根据放置的组合进行排序
     */
    public void sort() {
        // 排序 TODO 先进行新增设备数和联络长度排序 后续我们在通过其他的指标
        alternateOps.sort((op1, op2) -> {
            HashMap<String, Double> totalScope1 = op1.getLayTotalScope();
            HashMap<String, Double> totalScope2 = op2.getLayTotalScope();
            Double total1 = totalScope1.get("total");
            Double total2 = totalScope2.get("total");
            if (total1 == total2) {
                return totalScope1.get("contactLength") > totalScope2.get("contactLength") ? 1 : -1;
            } else {
                return total1 > total2 ? -1 : 1;
            }
        });
    }


    public List<BaseLay> getAllLay() {
        List<BaseLay> result = new ArrayList<>();
        for (PlanOperate alternateOp : alternateOps) {
            result.addAll(alternateOp.getKgLays());
        }
        return result;
    }


}
