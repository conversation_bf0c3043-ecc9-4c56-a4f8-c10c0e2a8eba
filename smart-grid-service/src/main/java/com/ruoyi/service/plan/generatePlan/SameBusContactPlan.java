package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.device.vo.FeederVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.findLay.contactLay.FindEndContactLay;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.ContactLayConfig;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.service.plan.model.sameBusContactPlan.CombReplaceBay;
import com.ruoyi.service.plan.utils.ListCombinations;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import com.ruoyi.service.plan.findLay.replaceBayLay.FindReplaceBayLay;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.service.znap.IZnapTopologyService;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 同母联络网架方案生成
 * 解决措施：
 * 1、与周边线路建立不同变电站或同变电站不同母线联络
 * 2、变电站/开闭所更换间隔为不同母联络线联络
 */
@Service
@Slf4j
public class SameBusContactPlan implements IGeneratePlan {

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    MapServiceImpl mapService;

    @Autowired
    IBayQueryService bayQueryService;

    @Autowired
    FindEndContactLay findEndContactLay;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) throws ExecutionException, InterruptedException {

        // =========================== 基础信息准备 =======================
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        Map<String, Node> nodeMap = nodePath.getNodeMap();

        double maxLoad = contactHandleService.getMaxFeederLoad();  // 当前配置最大负载率
        List<Node> allPb = topologyMap.getAllPb();

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(allPb);

        // 联络线路id集合
        List<String> contactFeederIds = nodePath.getContactFeederIds();
        HashMap<String, BusbarSwitchVo> contactBusKgMap = getBusKgMap(contactFeederIds); // 联络线路母线开关集合
        BusbarSwitchVo mainBusKg = getBusKg(feederId);// 主线路母线开关集合

        if (mainBusKg == null) {
            log.error("查询该线路母线异常,线路ID：{}", feederId);
            throw new RuntimeException("查询该线路母线异常！");
        }

        // 联络线路所属的母线 有 不存在和主线路母线相同的情况
//        if (contactBusKgMap.values().stream().anyMatch(d -> !StringUtils.equals(d.getBusbarId(), mainBusKg.getBusbarId()))) {
//            throw new RuntimeException("当前线路总" + contactFeederIds.size() + "条联络线路，都不为" + mainBusKg.getBusbarName() + "母线联络，该线路不是同母联络问题！");
//        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // (3)、推送：问题释义
        pushPlanProcessService.pushSomeBusExplain(problemId);

        // (4)、推送：同母联络识别
        pushPlanProcessService.pushSomeBusIdentify(problemId, deviceFeeder);

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        SurePlanOp surePlanOp = new SurePlanOp();

        // =========================== 更换间隔 =======================

        // 当前变电站下所有的母线开关
        List<BusbarSwitchVo> bdzBusKgs = getBdzBusKgs(feederId);

        // 可以更换间隔的开关（其他母线线的）
        List<BusbarSwitchVo> canBusKgs = getCanBusKgs(bdzBusKgs, mainBusKg.getBusbarId());

        List<PlanOperate> replaceBays = FindReplaceBayLay.findReplaceBays(mainBusKg, canBusKgs, contactBusKgMap, nodePath);

        // 更换间隔的
        if (CollectionUtils.isNotEmpty(replaceBays)) {
            surePlanOp.addAll(replaceBays);
        }

        // =========================== 临近线路新上联络 =======================

        // 新增联络线其目前值考虑负载相关即可（只是多考虑过滤同母线路的）
        List<ContactBranch> tfBranch = findEndContactLay.getFeederLoad(nodePath.getStartNode(), nodePath.getStartNextEdge(), feederNt, maxLoad, nodePath, null);

        CombAlternateOp contactCombOp = ContactLayPlanOp.getEndContactCombAltOpByBranches(tfBranch, nodePath, new ContactLayConfig(false, null, true));
        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(contactCombOp);

        processNodeService.handleLayOperateNode(surePlanOp, problemId, contactCombOp, token, feederId, nodePath);

        // =========================== 变电站新出线 =======================
        CombAlternateOp bdzNewLineCombAltOp = ContactLayPlanOp.getBdzNewLineCombAltOp(tfBranch, nodePath, new ContactLayConfig(false, null, true));
        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(bdzNewLineCombAltOp);

        processNodeService.handleLayOperateNode(surePlanOp, problemId, bdzNewLineCombAltOp, token, feederId, nodePath);

        // =========================== 方案生成 =======================

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        planProcessService.pushLoadingProcess(problemId, "方案集合按照开关数和联络距离排序");

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // 返回所有方案，不限制数量
        List<Plan> resultPlans = plans;

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        // 获取各个分支末端
        return resultPlans;
    }

    /**
     * 根据线路集合获取线路关联的母线开关
     */
    HashMap<String, BusbarSwitchVo> getBusKgMap(List<String> feederIds) {
        HashMap<String, BusbarSwitchVo> result = new HashMap<>();
        for (String feederId : feederIds) {
            BusbarSwitchVo busKg = getBusKg(feederId);
            if (busKg != null) {
                result.put(feederId, busKg);
            }
        }
        return result;
    }

    /**
     * 根据线路集合获取线路关联的母线开关
     */
    BusbarSwitchVo getBusKg(String feederId) {
        return bayQueryService.queryLineTopology(feederId);
    }

    /**
     * 获取该线路所属变电站下的所有母线开关
     */
    List<BusbarSwitchVo> getBdzBusKgs(String feederId) {
        return bayQueryService.querySubstationBusbarSwitches(feederId);
    }

    /**
     * 获取可以更换间隔的开关（其他母线线的）
     */
    List<BusbarSwitchVo> getCanBusKgs(List<BusbarSwitchVo> bdzBusKgs, String mainBusId) {
        // !StringUtils.equals(busKg.getBusbarId(), mainBusId ) &&
        return bdzBusKgs.stream().filter(busKg ->
                // 不是主母线 并且 是备用开关
                !StringUtils.equals(busKg.getBusbarId(), mainBusId) && busKg.getIsSpare()).collect(Collectors.toList());
    }

    List<String> getFilterFeederIds(List<DeviceFeeder> nearFeederList, String mainBusId) {
        List<String> nearFeederIds = nearFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        HashMap<String, BusbarSwitchVo> contactBusKgMap = getBusKgMap(nearFeederIds); // 联络线路母线开关集合
        List<String> filterFeederIds = new ArrayList<>();
        for (BusbarSwitchVo busKg : contactBusKgMap.values()) {
            if (StringUtils.equals(busKg.getBusbarId(), mainBusId)) {
                filterFeederIds.add(busKg.getFeederId());
            }
        }
        return filterFeederIds;
    }

}
