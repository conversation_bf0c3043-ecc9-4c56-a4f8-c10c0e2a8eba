package com.ruoyi.service.plan.utils;

import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 方案操作加工处理
 */
public class PlanOperateFactory {

    /**
     * 创建方案操作 根据插入layNode
     */
    public static PlanOperate createPlanOp(BaseLay layNode, String type) {
        PlanOperate planOperate = new PlanOperate(Collections.singletonList(layNode));
        planOperate.setType(type);
        return planOperate;
    }

    /**
     * 创建方案操作 根据插入layNode
     */
    public static PlanOperate createPlanOp(List<BaseLay> layNodes, String type) {
        PlanOperate planOperate = new PlanOperate(layNodes);
        planOperate.setType(type);
        return planOperate;
    }

    // =========================== 联络相关 =======================

    /**
     * 创建联络单
     */
    public static PlanOperate createContact(BaseLay layNode) {
        return createPlanOp(layNode, PlanOperate.CONTACT_TYPE);
    }

    /**
     * 创建联络操作相关 (会将部分路径转走)
     *
     * @param headKgLay  首开关
     * @param contactLay 联络线路lay
     */
    public static PlanOperate createBranchContact(BaseLay headKgLay, BaseLay contactLay) {
        List<BaseLay> lays = new ArrayList<>();
        if (headKgLay != null) {
            lays.add(headKgLay);
        }
        lays.add(contactLay);
        return createPlanOp(lays, PlanOperate.CONTACT_TYPE);
    }

    // =========================== 变电站新出线 =======================

    /**
     * 创建变电站新出现
     *
     * @param contactLay 联络线路lay
     */
    public static PlanOperate createBdzNewLine(BaseLay contactLay) {
        return createPlanOp(contactLay, PlanOperate.BDZ_NEW_LINE_TYPE);
    }

    /**
     * 创建变电站新出现
     *
     * @param headKgLay  首开关
     * @param contactLay 联络线路lay
     */
    public static PlanOperate createBdzNewLine(BaseLay headKgLay, BaseLay contactLay) {
        List<BaseLay> lays = new ArrayList<>();
        if (headKgLay != null) {
            lays.add(headKgLay);
        }
        lays.add(contactLay);
        return createPlanOp(lays, PlanOperate.BDZ_NEW_LINE_TYPE);
    }

    // =========================== 运放调整 =======================

    /**
     * 创建运放调整的
     *
     * @param layNode            layNode
     * @param combFeederTransfer 当前线路的专供对象
     */
    public static PlanOperate createRunAdjust(BaseLay layNode, CombFeederTransfer combFeederTransfer) {
        PlanOperate planOp = createPlanOp(layNode, PlanOperate.RUN_ADJUST_TYPE);
        planOp.setCombFeederTransfer(combFeederTransfer);
        return planOp;
    }

    // =========================== 开关 =======================

    /**
     * 创建分段开关的
     */
    public static PlanOperate createSegKg(BaseLay layNode) {
        return createPlanOp(layNode, PlanOperate.SEG_KG_TYPE);
    }

    /**
     * 创建分段开关的
     */
    public static PlanOperate createSegKg(List<BaseLay> layNodes) {
        return createPlanOp(layNodes, PlanOperate.SEG_KG_TYPE);
    }

    // =========================== 替换间隔 =======================

    /**
     * 替换间隔
     */
    public static PlanOperate createReplaceLay(BaseLay layNode) {
        return createPlanOp(layNode, PlanOperate.REPLACE_LAY_TYPE);
    }

    /**
     * 替换间隔
     */
    public static PlanOperate createReplaceLay(List<BaseLay> layNodes) {
        return createPlanOp(layNodes, PlanOperate.REPLACE_LAY_TYPE);
    }

    // =========================== 其它 =======================

    /**
     * 将多个PlanOps合并
     * 如果有多个不同的类型 那么就是组合类型的
     *
     * @param planOps 需要合并的
     */
    public static PlanOperate mergePlanOps(List<PlanOperate> planOps) {
        if (CollectionUtils.isEmpty(planOps)) {
            return null;
        }
        PlanOperate planOperate = new PlanOperate();
        String type = planOps.get(0).getType();
        for (PlanOperate planOp : planOps) {
            planOperate.addAll(planOp.getLayNodeList());
            if (!StringUtils.equals(type, planOp.getType())) {
                type = PlanOperate.COMB_TYPE;
            }
        }
        planOperate.setType(type);
        return planOperate;
    }
}
