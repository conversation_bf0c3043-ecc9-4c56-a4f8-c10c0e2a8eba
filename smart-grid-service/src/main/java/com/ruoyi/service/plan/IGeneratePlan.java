package com.ruoyi.service.plan;

import com.ruoyi.entity.plan.Plan;
import com.ruoyi.service.plan.model.plan.SurePlanOp;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface IGeneratePlan {
    /**
     * 生成方案
     */
    List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) throws ExecutionException, InterruptedException;

    // 合并
}
