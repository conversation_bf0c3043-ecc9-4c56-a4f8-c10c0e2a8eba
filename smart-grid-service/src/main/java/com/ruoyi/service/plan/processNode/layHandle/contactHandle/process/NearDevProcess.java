package com.ruoyi.service.plan.processNode.layHandle.contactHandle.process;

import com.ruoyi.entity.device.DevInfo;
import com.ruoyi.entity.map.ClosestNode;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.processNode.layHandle.ProcessUtils;
import com.ruoyi.util.NearDeviceInfoUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NearDevProcess {


    /**
     * 加工处理联络
     *
     * @param constantType 联络类型
     */
    public ProcessContactVo processHandle(NearNode nearNode, String constantType) {

        // 每个节点都单独处理
        ClosestNode closestNode = nearNode.getClosestNode();

        // 根据开始和结束节点 调用产生路由
        return processContactRoute(nearNode, closestNode, constantType);
    }

    /**
     * 加工联络路由
     */
    ProcessContactVo processContactRoute(NearNode nearNode, ClosestNode closestNode, String constantType) {
        Node node = nearNode.getNode();

        // 路由路径节点集合
        List<Node> nodeRouteNodeList = closestNode.getRouteNodeList();
        ProcessContactVo result = new ProcessContactVo();

        if (CollectionUtils.isEmpty(nodeRouteNodeList)) {
            return null;
        }

        // 附近设备
        NearbyDeviceInfoVo nearDev = (NearbyDeviceInfoVo) closestNode.getClosest();
        DevInfo contactDevInfo = NearDeviceInfoUtil.getContactDevInfo(nearDev);

        // 联络线路径节点加工
        List<Node> newPathNodes = processContactPaths(nodeRouteNodeList, node, constantType, contactDevInfo);

        // 生产加工路径对象
        result.setTotalLength(closestNode.getLength());
        result.setContactNodeList(newPathNodes);
        result.setStartPsrId(node.getPsrId());
        result.setEndPsrId(contactDevInfo.getPsrId());

        result.setEndStationPsrId(contactDevInfo.getStationPsrId());
        result.setEndStationPsrType(contactDevInfo.getStationPsrType());
        result.setEndStationPsrName(contactDevInfo.getStationPsrName());

        String[] feeder = NearDeviceInfoUtil.getFeeder(nearDev, contactDevInfo);
        result.setContactFeederId(feeder[0]);
        result.setContactFeederName(feeder[1]);
        result.setContactNode(node);

        return result;
    }

    /**
     * 加工联络线的路径节点集合
     *
     * @param nodes          路由路径集合节点
     * @param constantType   联络类型 参考ProcessContactBo里面的type
     * @param startNode      结束杆塔节点
     * @param contactDevInfo
     */
    List<Node> processContactPaths(List<Node> nodes, Node startNode, String constantType, DevInfo contactDevInfo) {

        // 1、设置拼接联络节点
        List<Node> result = ProcessUtils.processContactNodes(nodes, startNode, constantType, (linkNode) -> {
            linkNode.setContactFeeder(contactDevInfo.getFeederId(), contactDevInfo.getFeederName());
            return null;
        });

        // 2、处理末尾 结束设备可能是杆塔也可能是剩余的间隔
        if (nodes.size() > 2) {
            //  处理结束的设备
            Node endPole = ProcessUtils.toNode(contactDevInfo.getPsrId(), contactDevInfo.getPsrType(), contactDevInfo.getPsrName(), contactDevInfo.getLngLat());

            // 设置末尾节点的站房信息
            if (StringUtils.isNotBlank(contactDevInfo.getStationPsrId())) {
                endPole.setStation(contactDevInfo.getStationPsrId(), contactDevInfo.getStationPsrType(), contactDevInfo.getStationPsrName());
            }
            result.remove(result.size() - 1);
            Node edge = result.get(result.size() - 1);
            endPole.addEdge(edge, false);
            result.add(endPole);
        }
        return result;
    }

}
