package com.ruoyi.service.plan.generatePlan;

import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.device.vo.RunTowerPosition;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.lay.KgLay;
import com.ruoyi.service.plan.model.plan.AlternateOp;
import com.ruoyi.service.plan.model.plan.CombAlternateOp;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BaseGeneratePlan {

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    GeometryFactory geometryFactory = new GeometryFactory();

    // =========================== 基本运行数据 =======================
    // 加装放置位置的节点坐标 TODO 后续nodePath里面设置 这里就不需要设置了 后面会优化掉 现在先临时着
    public void layPositionCoords(CombAlternateOp combAlternateOp) {
        ArrayList<Node> poles = new ArrayList<>();
        HashMap<String, Node> poleMap = new HashMap<>();

        for (AlternateOp alternateOp : combAlternateOp.getAlternateOps()) {

            for (PlanOperate planSingleOperate : alternateOp.getAlternateOps()) {
                for (BaseLay layNode : planSingleOperate.getLayNodeList()) {
                    List<Node> range = new ArrayList<>();
                    if (layNode.isKgType()) {
                        KgLay kgLay = (KgLay) layNode;
                        range.add(kgLay.getBeforeNode());
                        range.add(kgLay.getAfterNode());
                    } else if (layNode.isContactType()) {
                        ContactLay contactLay = (ContactLay) layNode;
                        ProcessContactBo processContactBo = contactLay.getProcessContactBo();
                        if (contactLay.getContactBranch() != null) {
                            range = new ArrayList<>(contactLay.getContactBranch().getBranchNode().getNodes());
                        }
                        range.add(processContactBo.getPole());
                        SegBreakNodeBo segBreakNode = processContactBo.getSegBreakNode();
                        if (segBreakNode != null) {
                            range.add(segBreakNode.getStartNode());
                            range.add(segBreakNode.getEndNode());
                        }
                    }
                    range = range.stream().filter(Objects::nonNull).collect(Collectors.toList());

                    if (!CollectionUtils.isEmpty(range)) {
                        for (Node node : range) {
                            if (node.isPole()) {
                                if (node.getPsrId() != null && !poleMap.containsKey(node.getPsrId())) {
                                    poles.add(node);
                                    poleMap.put(node.getPsrId(), node);
                                }
                            }
                        }
                    }
                }
            }
        }

        // 杆塔节点本事没有坐标
        factoryPoleNodes(poles, poleMap);
    }


    /**
     * 杆塔节点本事没有坐标  这里去加工杆塔节点的坐标
     *
     * @param poles   杆塔
     * @param nodeMap 节点对象
     */
    private void factoryPoleNodes(List<Node> poles, Map<String, Node> nodeMap) {
        // 查询杆塔数据
        List<String> polePsrIds = poles.stream().map(Node::getPsrId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(polePsrIds)) {
            return;
        }
        List<RunTowerPosition> deviceRunTowers = deviceRunTowerMapper.selectPosition(polePsrIds);

        for (RunTowerPosition deviceRunTower : deviceRunTowers) {
            Node node = nodeMap.get(deviceRunTower.getPsrId());
            String geoPosition = deviceRunTower.getGeoPositon();
            if (node == null || node.getGeometry() != null) {
                continue;
            }
            if (geoPosition != null) {
                String[] split = geoPosition.split(",");
                Coordinate coordinate = new Coordinate(Double.valueOf(split[0]), Double.valueOf(split[1]));
                node.setGeometry(geometryFactory.createPoint(coordinate));
            }
        }
    }

    /**
     * 将所有的配变加工容量
     *
     * @param nodes
     */
    public void processPbNodeCap(List<Node> nodes) {
        List<Node> pbNodes = nodes.stream().filter(Node::isPb).collect(Collectors.toList());

        //计算每个分支的容量
        List<String> idList = pbNodes.stream().map(Node::getPsrId).collect(Collectors.toList());
        List<Double> caps = queryDeviceInfo.selectDeviceRatedCapacity(idList);

        // 加工容量 按照顺序
        for (int i = 0; i < caps.size(); i++) {
            pbNodes.get(i).setCap(caps.get(i));
        }
    }

    /**
     * 获取当前联络线相关的运行值
     */
    public List<FeederNtVo> getContactFeederNts(NodePath nodePath) {
        List<String> feederIds = nodePath.getContactFeederIds();

        return feederDeviceMapper.selectFeederNtsByFeederIds(feederIds);
    }

    /**
     * 获取当前联络线最大负载率
     */
    public Double getFeederMaxLoad(String feederId) {
        return feederDeviceMapper.selectLoad(feederId);
    }

}
