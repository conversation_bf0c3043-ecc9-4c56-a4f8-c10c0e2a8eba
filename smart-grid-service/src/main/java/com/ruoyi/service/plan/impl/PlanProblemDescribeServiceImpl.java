package com.ruoyi.service.plan.impl;

import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.cost.DeviceType;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.device.PBEntity;
import com.ruoyi.entity.device.vo.ByFeederBusBarAndBreaker;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.enuw.SupplyAreaEnum;
import com.ruoyi.entity.plan.vo.MarkVo;
import com.ruoyi.entity.plan.vo.PlanDeviceVo;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.plan.PlanProblemDescribeMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IPlanProblemDescribeService;
import com.ruoyi.service.problem.impl.ProblemIdentifyServiceImpl;
import com.ruoyi.service.znap.IZnapTopologyService;
import com.ruoyi.util.PlanProcessUtils;
import com.ruoyi.util.deviceCurve.SelectDeviceCurveUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.entity.cost.DeviceType.*;

@Service
@Slf4j
public class PlanProblemDescribeServiceImpl implements IPlanProblemDescribeService {

    @Autowired
    PlanProblemDescribeMapper planProblemDescribeMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    ProblemIdentifyServiceImpl problemIdentifyService;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    SelectDeviceCurveUtil selectDeviceCurveUtil;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Override
    public List<Object> selectDetails(Long id) {
        //查询问题，获得线路id以及二级编码
        Problem problem = planProblemDescribeMapper.selectProblem(id);
        try {
            //分段配变不合理
            if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_PB_MUSH_LEVEL)) {
                return getPBUnreasonable(problem);
                //大分支无联络
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_DFZWLN_MUSH_LEVEL)) {
                return getDFZ(problem);
                //线路挂接配变不合理 no
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_XLGJPB_MUSH_LEVEL)) {
                return getPylonsPBSegmentation(problem);
                //单辐射无联络
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_DFSXL_MUSH_LEVEL)) {
                return getNotifyOrderCreatedAsync(problem);
                //线路重过载
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_XLZGZ_MUSH_LEVEL)) {
                return getLineOverload(problem);
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.TRANSFER_LACK_LEVEL)) {
                // 专供能力不足
                return getTransferLackDesc(problem);
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_TMLN_MUSH_LEVEL)) {
                return getSameContact(problem);
            }
        } catch (
                Exception e) {
            log.error("该问题id匹配出现异常" + id);
            return PlanProcessUtils.toList("暂无问题！");
        }
        return PlanProcessUtils.toList("暂无问题！");
    }


    /**
     * 获取故障解决方案相关问题的不合理标记设备
     *
     * @param id 主键
     */
    @Override
    public List<HashMap<String, Object>> selectFaultMark(Long id) {
        //查询问题，获得线路id以及二级编码
        Problem problem = planProblemDescribeMapper.selectProblem(id);
        try {
            //分段配变不合理
            if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_PB_MUSH_LEVEL)) {
                return getPBUnreasonableMark(problem);
                //大分支无联络
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_DFZWLN_MUSH_LEVEL)) {
                return Arrays.asList(getBigBranchMark(problem));
                //线路挂接配变不合理
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_XLGJPB_MUSH_LEVEL)) {
                return Arrays.asList(getFeederPBMachMark(problem));
                //线路重过载
            } else if (problem.getCategoryLevel2Code().equals(PlanConstants.SEG_XLZGZ_MUSH_LEVEL)) {
                return Arrays.asList(getLineOverloadMark(problem));
            }
        } catch (
                Exception e) {
            log.error("该问题id匹配出现异常" + id);
        }
        return null;
    }

    /**
     * 分段配变不合理标记设备
     *
     * @param problem
     * @return
     */
    private List<HashMap<String, Object>> getPBUnreasonableMark(Problem problem) {

        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        if (singAnalysis == null) {
            return null;
        }
        //获取拓扑关系
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        List<HashMap<String, Object>> list = new ArrayList<>();

        //获取问题发生的所有分段信息
        List<SegBetween> segBetween = segBranchService.getMainSegBetweenList(nodePath, problem.getDeviceId(), nodePath.getContactKgNodes());
        for (SegBetween between : segBetween) {
            //封装返回实体
            HashMap<String, Object> markMap = new HashMap<>();
            markMap.put("startId", between.getStartNodeId());
            markMap.put("startPsrId", between.getStartPsrId());
            markMap.put("startPsrType", between.getStartPsrType());
            markMap.put("startPsrName", between.getStartPsrName());
            markMap.put("endId", between.getEndNodeId());
            markMap.put("endPsrId", between.getEndPsrId());
            markMap.put("endPsrType", between.getEndPsrType());
            markMap.put("endPsrName", between.getEndPsrName());
            //路径集合
            List<PlanDeviceVo> planPathVoList = between.getNodes().stream()
                    .filter(Node::isEdge)
                    .filter(node -> node.getPsrId() != null) // 过滤掉psrId为null的节点
                    .map(node -> {
                        PlanDeviceVo vo = new PlanDeviceVo();
                        vo.setId(node.getId());
                        vo.setDeviceName(node.getPsrName());
                        vo.setDeviceId(node.getPsrId());
                        vo.setDeviceType(node.getPsrType());
                        return vo;
                    })
                    .collect(Collectors.toList());
            //设备集合
            List<PlanDeviceVo> planDeviceVoList = between.getMainOtherNodes().stream()
                    .filter(Node::isPb)
                    .filter(node -> node.getPsrId() != null) // 过滤掉psrId为null的节点
                    .map(node -> {
                        PlanDeviceVo vo = new PlanDeviceVo();
                        vo.setId(node.getId());
                        vo.setDeviceName(node.getPsrName());
                        vo.setDeviceId(node.getPsrId());
                        vo.setDeviceType(node.getPsrType());
                        return vo;
                    })
                    .collect(Collectors.toList());

            // 处理配变容量
            handlePbCap(planDeviceVoList);
            markMap.put("planDeviceVoList", planDeviceVoList);
            markMap.put("planPathVoList", planPathVoList);
            list.add(markMap);
        }
        return list;
    }

    /**
     * 大分支标记
     *
     * @param problem
     * @return
     */
    private HashMap<String, Object> getBigBranchMark(Problem problem) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        if (singAnalysis == null) {
            return null;
        }
        //获取拓扑关系
        NodePath nodePath = singAnalysis.getNodePath();

        HashMap<String, Object> markVoMap = new HashMap<>();
        //获取问题发生的分段信息
        BranchNode branchNode = segBranchService.getMainBranch(nodePath, problem.getDeviceId());

        Node startNode = branchNode.getStartNode();
        if (startNode != null) {
            markVoMap.put("startId", startNode.getId());
            markVoMap.put("startPsrId", startNode.getPsrId());
            markVoMap.put("startPsrType", startNode.getPsrType());
            markVoMap.put("startPsrName", startNode.getPsrName());
        }

        // 大分子路径
        List<Node> branchPath = branchNode.getBranchPath();
        // 大分子配变
        List<Node> pbNodes = branchNode.getPbNodes();

        List<PlanDeviceVo> planPathVoList = branchPath.stream()
                .map(node -> {
                    PlanDeviceVo vo = new PlanDeviceVo();
                    vo.setId(node.getId());
                    vo.setDeviceName(node.getPsrName());
                    vo.setDeviceId(node.getPsrId());
                    vo.setDeviceType(node.getPsrType());
                    return vo;
                })
                .collect(Collectors.toList());

        List<PlanDeviceVo> planDeviceVoList = pbNodes.stream()
                .map(node -> {
                    PlanDeviceVo vo = new PlanDeviceVo();
                    vo.setId(node.getId());
                    vo.setDeviceName(node.getPsrName());
                    vo.setDeviceId(node.getPsrId());
                    vo.setDeviceType(node.getPsrType());
                    return vo;
                })
                .collect(Collectors.toList());

        // 处理配变容量
        handlePbCap(planDeviceVoList);

        markVoMap.put("planDeviceVoList", planDeviceVoList);
        markVoMap.put("planPathVoList", planPathVoList);
        return markVoMap;


    }


    /**
     * 线路挂接配变过多问题标记
     */
    private HashMap<String, Object> getFeederPBMachMark(Problem problem) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        if (singAnalysis == null) {
            return null;
        }
        //获取拓扑关系
        HashMap<String, Object> markVoMap = new HashMap<>();

        // 配变
        List<Node> pbNodes = new ArrayList<>();
        for (Node node : singAnalysis.getNodePath().getNodeList()) {
            if (node.isPb()) {
                pbNodes.add(node);
            }
        }

        // 路径
        List<Node> edgeNodes = new ArrayList<>();
        for (Node node : singAnalysis.getNodePath().getNodeList()) {
            if (node.isEdge()) {
                edgeNodes.add(node);
            }
        }

        List<PlanDeviceVo> planPathVoList = edgeNodes.stream()
                .map(node -> {
                    PlanDeviceVo vo = new PlanDeviceVo();
                    vo.setId(node.getId());
                    vo.setDeviceName(node.getPsrName());
                    vo.setDeviceId(node.getPsrId());
                    vo.setDeviceType(node.getPsrType());
                    return vo;
                })
                .collect(Collectors.toList());

        List<PlanDeviceVo> planDeviceVoList = pbNodes.stream()
                .map(node -> {
                    PlanDeviceVo vo = new PlanDeviceVo();
                    vo.setId(node.getId());
                    vo.setDeviceName(node.getPsrName());
                    vo.setDeviceId(node.getPsrId());
                    vo.setDeviceType(node.getPsrType());
                    return vo;
                })
                .collect(Collectors.toList());

        // 处理配变容量
        handlePbCap(planDeviceVoList);
        markVoMap.put("currentPbNum", planDeviceVoList.size());
        markVoMap.put("allowPbNum", iConditionService.pylonsPBNum());
        markVoMap.put("planDeviceVoList", planDeviceVoList);
        markVoMap.put("planPathVoList", planPathVoList);
        return markVoMap;
    }

    /**
     * 线路重过载问题标记
     */
    private HashMap<String, Object> getLineOverloadMark(Problem problem) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        if (singAnalysis == null) {
            return null;
        }
        //获取拓扑关系
        HashMap<String, Object> markVoMap = new HashMap<>();
        // 配变
        List<Node> pbNodes = new ArrayList<>();
        for (Node node : singAnalysis.getNodePath().getNodeList()) {
            if (node.isPb()) {
                pbNodes.add(node);
            }
        }

        // 路径
        List<Node> edgeNodes = new ArrayList<>();
        for (Node node : singAnalysis.getNodePath().getNodeList()) {
            if (node.isEdge()) {
                edgeNodes.add(node);
            }
        }
        List<PlanDeviceVo> planPathVoList = edgeNodes.stream()
                .map(node -> {
                    PlanDeviceVo vo = new PlanDeviceVo();
                    vo.setId(node.getId());
                    vo.setDeviceName(node.getPsrName());
                    vo.setDeviceId(node.getPsrId());
                    vo.setDeviceType(node.getPsrType());
                    return vo;
                })
                .collect(Collectors.toList());

        List<PlanDeviceVo> planDeviceVoList = pbNodes.stream()
                .map(node -> {
                    PlanDeviceVo vo = new PlanDeviceVo();
                    vo.setId(node.getId());
                    vo.setDeviceName(node.getPsrName());
                    vo.setDeviceId(node.getPsrId());
                    vo.setDeviceType(node.getPsrType());
                    return vo;
                })
                .collect(Collectors.toList());

        // 处理配变容量
        handlePbCap(planDeviceVoList);
        markVoMap.put("currentLoad", DoubleFormatter.formatToThreeDecimals2(feederDeviceMapper.selectLoad(problem.getFeederId()) * 100));
        markVoMap.put("allowPbLoad", iConditionService.lineOverloadNum());
        markVoMap.put("planDeviceVoList", planDeviceVoList);
        markVoMap.put("planPathVoList", planPathVoList);
        return markVoMap;
    }

    /**
     * 处理配变容量
     */
    private void handlePbCap(List<PlanDeviceVo> planDeviceVoList) {
        // 处理配变容量
        try {
            List<String> idList = planDeviceVoList.stream().map(PlanDeviceVo::getDeviceId).collect(Collectors.toList());

            // 查询所有配变
            List<PBEntity> vols = queryDeviceInfo.selectDevice(idList);

            // psrId映射对象
            Map<String, PBEntity> volMap = vols.stream().collect(Collectors.toMap(PBEntity::getPsrId, n -> n));

            for (PlanDeviceVo planDeviceVo : planDeviceVoList) {
                PBEntity val = volMap.get(planDeviceVo.getDeviceId());
                if (val != null) {
//                    planDeviceVo.setDeviceName(val.getName());
                    planDeviceVo.setCap(val.getRatedCapacity());
                    planDeviceVo.setPubPrivFlag(val.getPubPrivFlag());
                }
            }
        } catch (Exception e) {
            log.error("获取处理配变容量异常！", e);
        }
    }


    /**
     * 配变分段不合理的问题描述
     *
     * @param problem
     * @return
     */
    private List<Object> getPBUnreasonable(Problem problem) {
        //根据线路id查询供电区域
        DeviceFeeder feeder = planProblemDescribeMapper.selectArea(problem.getFeederId());
        //枚举获取对应的区域
        String supplyArea = SupplyAreaEnum.fromCode(feeder.getSupplyArea()).getGrade();

        //获取拓扑关系
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        List<Node> kgContactNodes = nodePath.getContactKgNodes();

        //获取分段信息
        SegBetween segBetween = segBranchService.getMainSegBetween(nodePath, problem.getDeviceId(), kgContactNodes);
        if (segBetween == null) {
            return PlanProcessUtils.toList("暂无问题！");
        }

        List<Node> nodeList = segBetween.getAllPb();
        //获取配变容量
        List<String> psrIdAndPsrTypes = nodeList.stream().map(Node::getPsrId).collect(Collectors.toList());

        List<Double> ratedCapacity = queryDeviceInfo.selectDeviceRatedCapacity(psrIdAndPsrTypes);

        Double capacity = ratedCapacity.stream().mapToDouble(Double::doubleValue).sum();

        List<Object> details = new ArrayList<>();

        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));

        details.add("所属");

        details.add(PlanProcessUtils.toStrikingStu(supplyArea));

        details.add("区域，分段");
        // 添加从哪个分段到哪个分段
        details.add(PlanProcessUtils.toTextBtnStu(segBetween.getStartPsrId(), segBetween.getStartPsrType(), segBetween.getStartPsrName(), null, null));
        details.add("至");
        details.add(PlanProcessUtils.toTextBtnStu(segBetween.getEndPsrId(), segBetween.getEndPsrType(), segBetween.getEndPsrName(), null, null));
        details.add("内挂接配变");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(segBetween.getAllPb().size())));

        details.add("台，装机容量");

        details.add(PlanProcessUtils.toStrikingStu(capacity.toString()));

        details.add("kVA，故障或检修停电范围大");

        int maxNums = iConditionService.unreasonablePBNum(feeder.getSupplyArea());

        details.add(PlanProcessUtils.toIgnoreStu(maxNums));

        return details;
    }


    /**
     * 大分支无联络的问题描述
     *
     * @param problem
     * @return
     */
    private List<Object> getDFZ(Problem problem) {
        //10kVXX线 XX支线 所挂接配变数为XX台，装机容量XXkVA，最大负荷为XXMW，且末端无联络，故障或检修停电范围大。

        //获取拓扑关系
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        NodePath nodePath = singAnalysis.getNodePath();

        //只保留大分支的数据（isMain ==true）
        BranchNode selectBranchNode = segBranchService.getMainBranch(nodePath, problem.getDeviceId());
//        List<BranchNode> dfz = branchNodeList.stream().filter(BranchNode::isMain).collect(Collectors.toList());

//        BranchNode selectBranchNode = dfz.stream()
//                .filter(branchNode -> branchNode.getNodes().stream()
//                        .anyMatch(node -> problem.getDeviceId().equals(node.getPsrId())))
//                .findFirst()
//                .orElse(null);

        if (selectBranchNode == null) {
            return PlanProcessUtils.toList("暂无问题！");
        }
        //获取配变容量
        List<String> idList = selectBranchNode.getPbNodes().stream().map(Node::getPsrId).collect(Collectors.toList());
        List<Object> details = new ArrayList<>();
        List<Double> ratedCapacity = queryDeviceInfo.selectDeviceRatedCapacity(idList);

        Double capacity = ratedCapacity.stream().mapToDouble(Double::doubleValue).sum();

        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));
//        details.add("首" + DeviceType.getDeviceTypeName(problem.getDeviceType()) + "为");
//        details.add(PlanProcessUtils.toTextBtnStu(problem.getDeviceId(), problem.getDeviceType(), problem.getDeviceName(), null, null));

        details.add("所挂接配变数为");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(selectBranchNode.getPbNodeNum())));

        details.add("台，装机容量");

        details.add(PlanProcessUtils.toStrikingStu(capacity.toString()));

        details.add("kVA，最大负荷为");

        details.add(PlanProcessUtils.toStrikingStu("——"));

        details.add("MW，且末端无联络，故障或检修停电范围大。");
        Integer minPbNum = iConditionService.bigBranchSegmentation();
        details.add(PlanProcessUtils.toIgnoreStu(minPbNum));
        return details;
    }

    /**
     * 线路挂接配变过多
     *
     * @param problem
     * @return
     */
    private List<Object> getPylonsPBSegmentation(Problem problem) {
        //10kVXX线所属X类供电区域，挂接配变数量XX台，线最大负载率XX%，线路总长XXkm，供电半径XXkm，末端XX、XX等配变存在低电压/XX年XX月XX日发生可靠性异常事件。

        //根据线路id查询供电区域
        DeviceFeeder feeder = planProblemDescribeMapper.selectArea(problem.getFeederId());

        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(problem.getFeederId());

        //枚举获取对应的区域
        String supplyArea = SupplyAreaEnum.fromCode(feeder.getSupplyArea()).getGrade();

        //获取拓扑关系
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        NodePath nodePath = singAnalysis.getNodePath();

        //获取配变数量
        int totalPbNodeNum = nodePath.getPbList().size();

        //获取线路最大负载率
        Double load = DoubleFormatter.formatToThreeDecimals2(feederDeviceMapper.selectLoad(problem.getFeederId()) * 100);

        List<Object> details = new ArrayList<>();

        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));

        details.add("所属");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(supplyArea)));

        details.add("类供电区域，挂接配变数量");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(totalPbNodeNum)));

        details.add("台，");
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        LocalDateTime dateTime = LocalDateTime.parse(feederNt.getHisMaxDate(), inputFormatter);
        // 格式化为目标字符串
        details.add("在");
        details.add(PlanProcessUtils.toStrikingStu(
                outputFormatter.format(dateTime)
        ));
        details.add("最大负载率达到");

        details.add(PlanProcessUtils.toStrikingStu(load.toString()));

        details.add("%，线路总长");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(feeder.getLength())));

        details.add("km，供电半径");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(feeder.getSupplyRadius())));

        details.add("km。");

        int maxPbNum = iConditionService.pylonsPBNum();// 最大的配变数量
        details.add(PlanProcessUtils.toIgnoreStu(maxPbNum));

        return details;
    }

    /**
     * 单辐射无联络的问题描述
     *
     * @param problem
     * @return
     */
    private List<Object> getNotifyOrderCreatedAsync(Problem problem) {

        //根据线路id查询供电区域
        DeviceFeeder feeder = planProblemDescribeMapper.selectArea(problem.getFeederId());

        //枚举获取对应的区域
        String supplyArea = SupplyAreaEnum.fromCode(feeder.getSupplyArea()).getGrade();

        // znap拓扑节点构建

        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        NodePath nodePath = singAnalysis.getNodePath();

        List<Node> nodeList = nodePath.getNodeList().stream().filter(Node::isPb)
                .collect(Collectors.toList());

        //计算配变容量

        List<Object> details = new ArrayList<>();
        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));

        details.add("所属");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(supplyArea)));

        details.add("类供电区域，该线单辐射无联络，装机容量");

        details.add(PlanProcessUtils.toStrikingStu(feeder.getFeederRateCapacity().toString()));

        details.add("kVA，配变挂接数量");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(nodeList.size())));

        details.add("台，故障或检修时负荷无法转供。");

        details.add(PlanProcessUtils.toIgnoreStu(nodeList.size()));

        return details;

    }

    /**
     * 线路重过载的问题描述
     *
     * @param problem
     * @return
     */
    private List<Object> getLineOverload(Problem problem) {

        //根据线路id查询供电区域
        DeviceFeeder feeder = planProblemDescribeMapper.selectArea(problem.getFeederId());
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(problem.getFeederId());

        // znap拓扑节点构建
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        NodePath nodePath = singAnalysis.getNodePath();

        List<Node> nodeList = nodePath.getNodeList().stream().filter(Node::isPb)
                .collect(Collectors.toList());

        double maxLoad = iConditionService.lineOverloadNum();   // 当前配置最大负载率


//        HashMap<String, String> hashMap = selectDeviceCurveUtil.selectLoad(problem.getDefectTime(), feeder.getPsrId(), 70, 24, DKX);
        List<Object> details = new ArrayList<>();
        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));
//        if (hashMap != null && StringUtils.isNotBlank(hashMap.get("sustainTime"))) {
//            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月dd日");
//            String formattedDate = outputFormat.format(problem.getDefectTime());
//            details.add("在" + formattedDate + hashMap.get("startTime") + "至" + formattedDate + hashMap.get("endTime"));
//
//            details.add("负载率持续");
//
//            details.add(hashMap.get("sustainTime"));
//
//            details.add("小时超70%，最大负载率达");
//
//            details.add(hashMap.get("maxLoad"));
//
//        }
//        if (hashMap != null && StringUtils.isBlank(hashMap.get("sustainTime"))) {
//            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月dd日");
//
//            String formattedDate = outputFormat.format(problem.getDefectTime());
//
//            details.add("在" + formattedDate + hashMap.get("maxTime"));
//
//            details.add("负载率");
//
//            details.add("超70%，最大负载率达");
//
//            details.add(PlanProcessUtils.toStrikingStu(hashMap.get("maxLoad")));
//        }
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 定义输出格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        // 解析日期字符串
        LocalDateTime dateTime = LocalDateTime.parse(feederNt.getHisMaxDate(), inputFormatter);
        // 格式化为目标字符串
        details.add("在");
        details.add(PlanProcessUtils.toStrikingStu(
                outputFormatter.format(dateTime)
        ));
        details.add("日最大负载率达到");

        details.add(PlanProcessUtils.toStrikingStu(
                feederNt.getHisMaxLoadRateStr()
        ));

        details.add("%，");

        details.add("装机容量");

        details.add(PlanProcessUtils.toStrikingStu(feeder.getFeederRateCapacity().toString()));

        details.add("kVA，配变挂接数量");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(nodeList.size())));

        details.add("台，光伏接入容量");

        details.add(PlanProcessUtils.toStrikingStu("——"));

        details.add("kW，为重过载线路。");

        details.add(PlanProcessUtils.toIgnoreStu(nodeList.size()));

        return details;

    }

    /**
     * 同母联络的问题描述
     *
     * @param problem
     * @return
     */
    private List<Object> getSameContact(Problem problem) throws IOException {
        //根据线路id查询线路信息
        DeviceFeeder feeder = planProblemDescribeMapper.selectArea(problem.getFeederId());

        //枚举获取对应的区域
        String supplyArea = SupplyAreaEnum.fromCode(feeder.getSupplyArea()).getGrade();

        //查询线路相关的母线和变电站
        ByFeederBusBarAndBreaker byFeederBusBarAndBreaker = queryDeviceInfo.selectBusBarAndBreaker(problem.getFeederId());

        //  SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        //   NodePath nodePath = singAnalysis.getNodePath();

        //联络线id
        //  List<String> contactIdList = nodePath.getContactFeederIds();
        //   List<DeviceFeeder> contactDeviceFeederList = feederDeviceMapper.selectByIds(contactIdList);

        List<Object> details = new ArrayList<>();

        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));

        details.add("所属");

        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(supplyArea)));

        details.add("类供电区域，该线路下的联络线");

//        for (int i = 0; i < contactDeviceFeederList.size(); i++) {
//            details.add(PlanProcessUtils.toTextBtnStu(contactDeviceFeederList.get(i).getPsrId(), DKX, contactDeviceFeederList.get(i).getName(), null, null));
//            if (i != contactDeviceFeederList.size() - 1) {
//                details.add("、");
//            }
//
//        }
        details.add("均来自");
        details.add(PlanProcessUtils.toTextBtnStu(byFeederBusBarAndBreaker.getSubstationId(), "zf01", byFeederBusBarAndBreaker.getSubstationName(), null, null));
        details.add("->");
        details.add(PlanProcessUtils.toTextBtnStu(byFeederBusBarAndBreaker.getBusBarId(), BUS, byFeederBusBarAndBreaker.getBusBarName(), null, null));

        details.add("，仅有同母联络，母线故障时线负荷无法转供。");


//        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(nodeList.size())));

//        details.add("台，故障或检修时负荷无法转供。");

        return details;
    }

    /**
     * 专供能力不足问题描述
     *
     * @param problem
     * @return
     */
    private List<Object> getTransferLackDesc(Problem problem) {

        //根据线路id查询供电区域
        DeviceFeeder feeder = planProblemDescribeMapper.selectArea(problem.getFeederId());
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(problem.getFeederId());

        // znap拓扑节点构建
        SingAnalysis singAnalysis = singMapService.analysisSingMap(problem.getFeederId(), false);
        NodePath nodePath = singAnalysis.getNodePath();
        List<Node> pbList = nodePath.getPbList();

        List<Object> details = new ArrayList<>();
        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 定义输出格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        // 解析日期字符串
        LocalDateTime dateTime = LocalDateTime.parse(feederNt.getHisMaxDate(), inputFormatter);
        // 格式化为目标字符串
        details.add(PlanProcessUtils.toStrikingStu(
                outputFormatter.format(dateTime)
        ));
        details.add("日最大负载率达到");
        details.add(PlanProcessUtils.toStrikingStu(
                feederNt.getHisMaxLoadRateStr()
        ));
        details.add("%，");
        details.add("装机容量");
        details.add(PlanProcessUtils.toStrikingStu(feeder.getFeederRateCapacity().toString()));
        details.add("kVA，配变挂接数量");
        details.add(PlanProcessUtils.toStrikingStu(String.valueOf(pbList.size())));
        details.add("台，");

        // TODO 需要查询
        //  details.add("联络线YY线XX年最大负载率达XX%");

        details.add(PlanProcessUtils.toTextBtnStu(problem.getFeederId(), DKX, problem.getFeederName(), null, null));
        details.add("线不满足“N-1”校验要求");
        return details;
    }
}
