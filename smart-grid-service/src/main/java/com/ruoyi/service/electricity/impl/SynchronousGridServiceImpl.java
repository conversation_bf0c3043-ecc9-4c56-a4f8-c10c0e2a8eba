package com.ruoyi.service.electricity.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.device.DeviceAccessPoint;
import com.ruoyi.entity.device.DeviceCableTerminalJoint;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceWlgt;
import com.ruoyi.entity.electricity.AnalysisCust;
import com.ruoyi.entity.map.ApiResponse;
import com.ruoyi.entity.map.NearFeeder;
import com.ruoyi.entity.map.Point;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.entity.map.vo.RouteVo;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.mapper.device.DeviceCableTerminalJointMapper;
import com.ruoyi.mapper.device.DeviceWlgtMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.electricity.ElectricityAnalysisMapper;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.mapper.power.PowerMapper;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.service.electricity.ISynchronousGridService;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.service.problem.IProblemIdentifyService;
import com.ruoyi.util.Post.UgscMapApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SynchronousGridServiceImpl implements ISynchronousGridService {
    @Autowired
    ProblemMapper problemMapper;

    @Autowired
    PowerMapper powerMapper;

    @Autowired
    ElectricityAnalysisMapper electricityAnalysisMapper;

    @Autowired
    private ExecutorService executorService; // 注入自定义线程池

    @Autowired
    private DeviceCableTerminalJointMapper deviceCableTerminalJointMapper;

    @Autowired
    DeviceWlgtMapper deviceWlgtMapper;


    @Autowired
    NearFeederMapper nearFeederMapper;

    @Autowired
    IMapService mapService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IProblemIdentifyService iProblemIdentifyService;


    /**
     * 同步行业用户的网格
     */
    @Override
    public boolean synchronousCustAsGrid() {
        List<String> list = problemMapper.selectGrid();

        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectList(list);

        List<AnalysisCust> analysisCustList = electricityAnalysisMapper.selectAll();

        List<DeviceAccessPoint> deviceAccessPointList = electricityAnalysisMapper.selectDeviceAccessPoint(analysisCustList.stream().map(e -> String.valueOf(e.getCustNo())).collect(Collectors.toList()));

        // 1. 空列表判断
        if (dmsFeederDeviceList == null || deviceAccessPointList == null) {
            return false;
        }

        // 2. 构建DeviceFeeder映射表（包含空psrId过滤）
        Map<String, DeviceFeeder> feederMap = new HashMap<>();
        for (DeviceFeeder feeder : dmsFeederDeviceList) {
            if (feeder != null && feeder.getPsrId() != null) {
                feederMap.put(feeder.getPsrId(), feeder);
            }
        }

        // 3. 遍历并赋值（包含空feeder过滤）
        for (DeviceAccessPoint accessPoint : deviceAccessPointList) {
            if (accessPoint != null && accessPoint.getFeeder() != null) {
                DeviceFeeder feeder = feederMap.get(accessPoint.getFeeder());
                if (feeder != null && feeder.getGridCode() != null) {
                    accessPoint.setGridCode(feeder.getGridCode());
                }
            }
        }


        // 1. 空列表检查
        if (analysisCustList == null || deviceAccessPointList == null) {
            return false;
        }

        // 2. 按userId分组DeviceAccessPoint
        Map<String, List<DeviceAccessPoint>> userIdToAccessPointsMap = deviceAccessPointList.stream()
                .filter(ap -> ap != null && ap.getUserId() != null) // 过滤空对象和空userId
                .collect(Collectors.groupingBy(DeviceAccessPoint::getUserId));

        // 3. 遍历AnalysisCust并赋值gridCode
        analysisCustList.stream()
                .filter(cust -> cust != null && cust.getCustNo() != null) // 过滤空对象和空custNo
                .forEach(cust -> {
                    List<DeviceAccessPoint> matchedAccessPoints = userIdToAccessPointsMap.get(cust.getCustNo().toString());

                    if (matchedAccessPoints != null && !matchedAccessPoints.isEmpty()) {
                        // 拼接所有匹配的gridCode，用逗号分隔
                        String concatenatedGridCodes = matchedAccessPoints.stream()
                                .map(DeviceAccessPoint::getGridCode)
                                .filter(Objects::nonNull) // 过滤空gridCode
                                .distinct() // 去重
                                .collect(Collectors.joining(","));

                        cust.setGridCode(concatenatedGridCodes.isEmpty() ? null : concatenatedGridCodes);
                    } else {
                        cust.setGridCode(null); // 没有匹配项则设为null
                    }
                });


        electricityAnalysisMapper.updateBatchById(analysisCustList);

        return true;
    }


    /**
     * 物理杆塔的坐标换成国网坐标
     */
    @Override
    public void wlgtCoordinates() throws Exception {
        List<List<DeviceWlgt>> wlgtList = getAllPageDeviceWlgtAsync();
        for (List<DeviceWlgt> deviceWlgts : wlgtList) {
            List<String> geoList = deviceWlgts.stream().map(DeviceWlgt::getGeoPositon).collect(Collectors.toList());

            List<String> idList = deviceWlgts.stream().map(DeviceWlgt::getAstId).collect(Collectors.toList());
            Map<String, String> updateMap = coordinates(geoList, idList);

            List<DeviceWlgt> updateList = new ArrayList<>();
            updateMap.forEach((key, value) -> {
                DeviceWlgt update = new DeviceWlgt();
                update.setAstId(key);
                update.setGeoPositon(value);
                update.setState(1);
                updateList.add(update);
            });
            deviceWlgtMapper.batchUpdateGeoAndState(updateList);
            Thread.sleep(1000);

        }


    }

    /**
     * 终端的坐标换成国网坐标
     */
    @Override
    public void cableTerminalJointcoordinates() throws Exception {
        List<List<DeviceCableTerminalJoint>> jointList = getAllPageDeviceCableTerminalJointAsync();
        for (List<DeviceCableTerminalJoint> joints : jointList) {
            List<String> geoList = joints.stream().map(DeviceCableTerminalJoint::getGeoPosition).collect(Collectors.toList());

            List<String> idList = joints.stream().map(DeviceCableTerminalJoint::getPsrId).collect(Collectors.toList());
            Map<String, String> updateMap;
            try {
               updateMap = coordinates(geoList, idList);
            } catch (Exception e) {
                continue;
            }

            List<DeviceCableTerminalJoint> updateList = new ArrayList<>();
            updateMap.forEach((key, value) -> {
                DeviceCableTerminalJoint update = new DeviceCableTerminalJoint();
                update.setPsrId(key);
                update.setGeoPosition(value);
                update.setState("1");
                updateList.add(update);
            });
            deviceCableTerminalJointMapper.batchUpdateGeoAndState(updateList);
            Thread.sleep(1000);

        }
    }

    /**
     * 同步的坐标
     *
     * @return
     */
    public Map<String, String> coordinates(List<String> geoList, List<String> idList) throws Exception {

        String coords = String.join(";", geoList);

        String[] parts = coords.split("\n");
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            result.append(parts[i]);

        }
        String finalResult = result.toString();

        String response = UgscMapApiClient.sendPostRequest("https://map.sgcc.com.cn/geoconv/v2",
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rg7Rv4rZ_hjyfjhM1fNeaFsghHekjn162cfTdqmT1Vo",
                finalResult,
                "1");
        List<String> stringList = geo(response);

        if (idList.size() != stringList.size()) {
            throw new IllegalArgumentException("ID列表和地理位置列表长度必须一致");
        }

        Map<String, String> updateMap = new HashMap<>();
        for (int i = 0; i < idList.size(); i++) {
            updateMap.put(idList.get(i), stringList.get(i));

        }
        return updateMap;

    }


    /**
     * 解析sj坐标
     *
     * @param jsonResponse
     * @return
     */
    public static List<String> geo(String jsonResponse) {

        // 方法1：直接解析为对象
        ApiResponse response = JSON.parseObject(jsonResponse, ApiResponse.class);
        List<Point> points = response.getValue();
        List<String> stringList = new ArrayList<>();

        for (Point point : points) {
            stringList.add(point.getX() + "," + point.getY());

        }
        return stringList;
    }

    // 动态分段异步查询所有物理杆塔
    public List<List<DeviceWlgt>> getAllPageDeviceWlgtAsync() {
        // 1. 先查询总记录数
        int totalCount = deviceWlgtMapper.getWlgtCount();

        // 2. 根据总记录数1和每段大小计算分段数
        int batchSize = 200; // 每批查询数量
        int batches = (int) Math.ceil((double) totalCount / batchSize);

        // 3. 创建异步任务列表
        List<CompletableFuture<List<DeviceWlgt>>> futures = new ArrayList<>();
        for (int i = 0; i < batches; i++) {
            final int offset = i * batchSize;
            futures.add(CompletableFuture.supplyAsync(() ->
                    deviceWlgtMapper.queryRangeWlgt(offset, batchSize), executorService)
            );
        }

        return futures.stream()
                .map(CompletableFuture::join)  // 将每个 future 转换为 List<DeviceFeeder>
                .collect(Collectors.toList());
    }

    // 动态分段异步查询所有终端
    public List<List<DeviceCableTerminalJoint>> getAllPageDeviceCableTerminalJointAsync() {
        // 1. 先查询总记录数
        int totalCount = deviceWlgtMapper.getCableTerminalJointCount();

        // 2. 根据总记录数1和每段大小计算分段数
        int batchSize = 200; // 每批查询数量
        int batches = (int) Math.ceil((double) totalCount / batchSize);

        // 3. 创建异步任务列表
        List<CompletableFuture<List<DeviceCableTerminalJoint>>> futures = new ArrayList<>();
        for (int i = 0; i < batches; i++) {
            final int offset = i * batchSize;
            futures.add(CompletableFuture.supplyAsync(() ->
                    deviceWlgtMapper.queryRangeTerminalJoint(offset, batchSize), executorService)
            );
        }

        return futures.stream()
                .map(CompletableFuture::join)  // 将每个 future 转换为 List<DeviceCableTerminalJoint>
                .collect(Collectors.toList());
    }

    /**
     * 同步某个线路附近4公里所有线路
     *
     * @param feederId
     * @return
     */
    @Override
    public void nearFeeder(String feederId) {
        LambdaQueryWrapper<NearFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(NearFeeder::getFeederId, feederId);

        if (nearFeederMapper.selectOne(lambdaQueryWrapper) != null) {
            return;
        }
        FeederRangeQueryBo feederRangeQueryBo = new FeederRangeQueryBo();
        feederRangeQueryBo.setPsrId(feederId);
        feederRangeQueryBo.setRange(4.0);
        feederRangeQueryBo.setNum(-1);

        List<NeedFeederVo> feederList = mapService.feederRangeQuery(feederRangeQueryBo);

        if (CollectionUtils.isEmpty(feederList)) {
            return;
        }

        if (feederList != null) {
            List<String> idList = feederList.stream().map(NeedFeederVo::getPsrId).collect(Collectors.toList());
            NearFeeder nearFeeder = new NearFeeder();
            nearFeeder.setFeederId(feederId);
            nearFeeder.setNearFeederId(String.join(",", idList));
            nearFeederMapper.insert(nearFeeder);

        }

    }

    @Override
    public void test() {

        List<String> idList = feederDeviceMapper.selectList().stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        for (String s : idList) {
            nearFeeder(s);
        }
    }

    /**
     * 再此确认导入的问题是否是我们配置的规则下的问题
     */
    @Override
    public void problemMyRule() {
        //查询出对应的二级分类的问题
        List<Integer> integerList = new ArrayList<>();
//        integerList.add(PlanConstants.SEG_PB_MUSH_LEVEL);
//        integerList.add(PlanConstants.SEG_DFZWLN_MUSH_LEVEL);
//        integerList.add(PlanConstants.SEG_XLGJPB_MUSH_LEVEL);
        integerList.add(PlanConstants.SEG_DFSXL_MUSH_LEVEL);
        LambdaQueryWrapper<Problem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Problem::getCategoryLevel2Code, integerList);

        List<Problem> problemList = problemMapper.selectList(lambdaQueryWrapper);
        //判断二级分类问题是否符合我们的规则配置
        processProblemsInBatches(problemList);
        problemMapper.updateBatchById(problemList);
    }


    /**
     * 异步多线程进行匹配规则
     *
     * @param problemList
     */
    public void processProblemsInBatches(List<Problem> problemList) {
        if (problemList == null || problemList.isEmpty()) {
            return;
        }

        // 每批处理500条数据
        int batchSize = 500;
        int totalSize = problemList.size();

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(
                Math.min(10, Runtime.getRuntime().availableProcessors() * 2)
        );

        try {
            // 分批提交任务
            List<Future<?>> futures = new ArrayList<>();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<Problem> batch = problemList.subList(i, endIndex);

                futures.add(executor.submit(() -> {
                    // 处理单批数据
                    processBatch(batch);
                }));
            }
            // 等待所有批次完成
            for (Future<?> future : futures) {
                future.get(); // 会抛出异常
            }
        } catch (Exception e) {
            log.error("批量处理失败", e);
            throw new RuntimeException("批量处理失败", e);
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 匹配规则
     *
     * @param batch
     */
    private void processBatch(List<Problem> batch) {
        for (Problem problem : batch) {
            try {
                Integer category = problem.getCategoryLevel2Code();

                if (PlanConstants.SEG_PB_MUSH_LEVEL == category) {
                    problem.setProblemMyRule(
                            iProblemIdentifyService.unreasonablePBSegmentation(problem.getFeederId(), problem.getDeviceId())
                    );
                } else if (PlanConstants.SEG_DFZWLN_MUSH_LEVEL == category) {
                    problem.setProblemMyRule(
                            iProblemIdentifyService.bigBranchSegmentation(problem.getFeederId(), problem.getDeviceId())
                    );
                } else if (PlanConstants.SEG_XLGJPB_MUSH_LEVEL == category) {
                    problem.setProblemMyRule(
                            iProblemIdentifyService.pylonsPBSegmentation(problem.getFeederId())
                    );
                }else if (PlanConstants.SEG_DFSXL_MUSH_LEVEL == category) {
                    problem.setProblemMyRule(
                            iProblemIdentifyService.singleRadiationSegmentation(problem.getFeederId())
                    );
                }
            } catch (Exception e) {
                log.error("处理馈线ID: {} 时出现异常", problem.getFeederId(), e);
            }
        }
    }


}
