package com.ruoyi.service.problem.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.constant.SegmentationEnum;
import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.vo.ByFeederBusBarAndBreaker;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.enuw.SupplyAreaEnum;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.entity.znap.ConDmsFeeder;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.TpMainPathCb;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.mapper.znap.ConDmsFeederMapper;
import com.ruoyi.mapper.znap.TpMainPathCbMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.generatePlan.BaseGeneratePlan;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import com.ruoyi.service.problem.IProblemIdentifyService;
import com.ruoyi.service.znap.IZnapTopologyService;
import com.ruoyi.util.deviceCurve.SelectDeviceCurveUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.constant.PlanConstants.*;
import static com.ruoyi.entity.cost.DeviceType.DKX;

@Service
public class ProblemIdentifyServiceImpl implements IProblemIdentifyService {


    @Autowired
    ISingMapService singMapService;


    @Autowired
    FeederDeviceMapper feederDeviceMapper;
    /**
     * 主干开关路径
     */
    @Autowired
    private TpMainPathCbMapper tpMainPathCbMapper;

    /**
     * 馈线
     */
    @Autowired
    private ConDmsFeederMapper conDmsFeederMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    SelectDeviceCurveUtil selectDeviceCurveUtil;

    @Autowired
    ProblemMapper problemMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    /**
     * 传入问题判断，其是否符合我们规则
     */
    @Override
    public Integer checkProblem(Long problemId) throws IOException {
        //根据问题查询id
        Problem problem = problemMapper.selectById(problemId);

        try {
            //分段配变不合理
            if (problem.getCategoryLevel2Code().equals(SEG_PB_MUSH_LEVEL)) {
                return unreasonablePBSegmentation(problem.getFeederId(), problem.getDeviceId());
            }
            //线路挂接配变过多
            else if (problem.getCategoryLevel2Code().equals(SEG_XLGJPB_MUSH_LEVEL)) {
                return pylonsPBSegmentation(problem.getFeederId());
            }
            //单辐射无联络
            else if (problem.getCategoryLevel2Code().equals(SEG_DFSXL_MUSH_LEVEL)) {
                return singleRadiationSegmentation(problem.getFeederId());
            }
            //大分支无联络
            else if (problem.getCategoryLevel2Code().equals(SEG_DFZWLN_MUSH_LEVEL)) {
                return bigBranchSegmentation(problem.getFeederId(), problem.getDeviceId());
            }
            //线路重过载
            else if (problem.getCategoryLevel2Code().equals(SEG_XLZGZ_MUSH_LEVEL)) {
                return lineOverload(problem.getFeederId());
            }
            //同母联络
            else if (problem.getCategoryLevel2Code().equals(SEG_TMLN_MUSH_LEVEL)) {
                return sameContact(problem.getFeederId());
            }
            // 转供能力不足
            else if (problem.getCategoryLevel2Code().equals(TRANSFER_LACK_LEVEL)) {
                return transferLack(problem.getFeederId());
            }
        } catch (IOException e) {
            return SegmentationEnum.NO_PROBLEM;
        }

        return null;
    }


    /**
     * 根据线路id分析其是否是分段配变不合理的问题
     */
    @Override
    public Integer unreasonablePBSegmentation(String feederId, String deviceId) {
        //获取拓扑关系
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        if (singAnalysis == null) {
            return SegmentationEnum.LINE_ABNORMAL;
        }

        //获取分段信息
        SegBetween segBetween = null;
        try {
            segBetween = segBranchService.getMainSegBetween(singAnalysis.getNodePath(), deviceId, null);
        } catch (Exception e) {
            return SegmentationEnum.NO_PROBLEM;
        }
        // 表示没识别到（单线图更改了，那么我们就认为没有问题）
        if (segBetween == null) {
            return SegmentationEnum.NO_PROBLEM;
        }
        //查询线路信息主要是区域
        DeviceFeeder feeder = feederDeviceMapper.selectById(feederId);
        if (feeder == null) {
            return SegmentationEnum.LINE_ABNORMAL;
        }

        //计算分段下的配变数量，并且判断是否合理
        if (segBetween.getAllPb().size() >= iConditionService.unreasonablePBNum(feeder.getSupplyArea())) {
            return SegmentationEnum.SOMETHING;
        }
        return SegmentationEnum.NO_PROBLEM;
    }

    /**
     * 根据线路id分析其是否是挂架配变过多识别 (0,线路拓扑解析异常；1，无问题，2有问题)
     */
    @Override
    public Integer pylonsPBSegmentation(String feederId) {
        //获取拓扑关系
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        if (singAnalysis == null) {
            return SegmentationEnum.LINE_ABNORMAL;
        }

        //配变计数
        int county = singAnalysis.getNodePath().getPbList().size();

        //判断配变是否超过最小值
        if (county >= iConditionService.pylonsPBNum()) {
            return SegmentationEnum.SOMETHING;
        }
        return SegmentationEnum.NO_PROBLEM;
    }

    /**
     * 根据线路id分析其是否是单辐射无联络问题,有联络开关则是无问题，没有联络开关有问题  (0,线路拓扑解析异常；1，无问题，2有问题)
     */
    @Override
    public Integer singleRadiationSegmentation(String feederId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        List<Node> contactKgNodes = nodePath.getContactKgNodes();
        if (CollectionUtils.isEmpty(contactKgNodes)) {
            return SegmentationEnum.SOMETHING;
        }
        return SegmentationEnum.NO_PROBLEM;
    }

    /**
     * 根据线路id分析其是否是大分支无联络问题 (0,线路拓扑解析异常；1，无问题，2有问题)
     */
    @Override
    public Integer bigBranchSegmentation(String feederId, String deviceId) {
        //获取拓扑关系

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        if (singAnalysis == null) {
            return SegmentationEnum.LINE_ABNORMAL;
        }
        NodePath nodePath = singAnalysis.getNodePath();
        //获取问题设备所在的大分支
        BranchNode branchNode = segBranchService.getMainBranch(nodePath, deviceId);
        //空处理
        if (branchNode == null) {
            return SegmentationEnum.NO_PROBLEM;
        }
        // TODO 这里需要判断这个分支线是否有联络开关  如果有联络开关量 那么不属于问题了滴
//        List<Node> kgContactNodes = nodePath.getContactKgNodes();
//        //检查此分支里面是否有联络开关，如果有就无问题
//        Set<String> contactNodeIds = kgContactNodes.stream()
//                .map(Node::getPsrId)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toSet());
//
//        boolean hasMatch = branchNode.getNodes().stream()
//                .map(Node::getPsrId)
//                .filter(Objects::nonNull)
//                .anyMatch(contactNodeIds::contains);
//        if (hasMatch) {
//            return SegmentationEnum.NO_PROBLEM;
//        }

        //判断条件为挂接配变是否出现问题
        if (branchNode.getPbNodeNum() >= iConditionService.bigBranchSegmentation()) {
            return SegmentationEnum.SOMETHING;
        }

        //计算每个分支的容量
        List<String> idList = branchNode.getPbNodes().stream().map(Node::getPsrId).collect(Collectors.toList());
        List<Double> ratedCapacity = queryDeviceInfo.selectDeviceRatedCapacity(idList);
        Double capacity = ratedCapacity.stream().mapToDouble(Double::doubleValue).sum();

        //判断条件为容量是否出现问题
        if (capacity >= iConditionService.bigBranchCapacityNum()) {
            return SegmentationEnum.SOMETHING;
        }
        return SegmentationEnum.NO_PROBLEM;
    }

    /**
     * 根据线路id分析其是否是线路重过载问题
     *
     * @param feederId
     * @return
     */
    @Override
    public Integer lineOverload(String feederId) {
        //线路历史最大负载率
        Double historyMaxLoad = feederDeviceMapper.selectLoad(feederId) * 100;
        //线路允许最大的负载率
        Integer allowMaxLoad = iConditionService.lineOverloadNum();

        //历史和允许最大负载率任意一个为空都有问题
        if (historyMaxLoad == null || allowMaxLoad == null) {
            return SegmentationEnum.LINE_ABNORMAL;
        }
        //如果历史最大负载率比允许的大则有问题
        if (historyMaxLoad > allowMaxLoad) {
            return SegmentationEnum.SOMETHING;
        }
        return SegmentationEnum.NO_PROBLEM;
    }

    /**
     * 根据线路id分析其是否是同母联络问题
     *
     * @param feederId
     * @return
     */
    @Override
    public Integer sameContact(String feederId) throws IOException {
        return SegmentationEnum.SOMETHING;

//        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
//        NodePath nodePath = singAnalysis.getNodePath();
//        //所有联络线id
//        List<String> contactIdList = nodePath.getContactFeederIds();
//        //TODO 如果目标线下没有联络线现在默认是无问题的
//        if (CollectionUtils.isEmpty(contactIdList)) {
//            return SegmentationEnum.NO_PROBLEM;
//        }
//        //加上自身线路id
//        contactIdList.add(feederId);
//
//        //查找所有线路的母线
//        //TODO 如果线路差不多对应的母线，则默认和目标线路是同一个母线；如果都查不到则认为都在一个母线下
//        List<ByFeederBusBarAndBreaker> byFeederBusBarAndBreakers = new ArrayList<>();
//        for (String id : contactIdList) {
//            //查询线路相关的母线和变电站
//            ByFeederBusBarAndBreaker byFeederBusBarAndBreaker = queryDeviceInfo.selectBusBarAndBreaker(id);
//            if (byFeederBusBarAndBreaker != null) {
//                byFeederBusBarAndBreakers.add(byFeederBusBarAndBreaker);
//            }
//
//        }
//        //如果都查不多则认为在一个母线下
//        if (CollectionUtils.isEmpty(byFeederBusBarAndBreakers)) {
//            return SegmentationEnum.SOMETHING;
//        }
//
//        // 取出所有的母线id组成set
//        Set<String> busBarIdSet = byFeederBusBarAndBreakers.stream()
//                .map(ByFeederBusBarAndBreaker::getBusBarId)  // 提取 busBarId
//                .collect(Collectors.toSet());                 // 收集到 Set 中去重
//
//        //如果母线数量大于1，则没有问题
//        if (busBarIdSet.size() > 1) {
//            return SegmentationEnum.NO_PROBLEM;
//        }
//        //剩下的只有等于1的情况，等于1则有问题
//        return SegmentationEnum.SOMETHING;
    }

    /**
     * 根据线路id分析其是否是转供能力不足问题
     *
     * @param feederId
     * @return
     */
    public Integer transferLack(String feederId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);

        double maxLoad = contactHandleService.getMaxFeederLoad();  // 当前配置最大负载率

        // 获取所有联络线的最大量测值
        List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(nodePath);

        //  获取可以运方调整的联络线开关
        List<ContactFeederKg> canContactFeederKgs = contactHandleService.getCanContactFeederKg(feederId, nodePath, contactFeederNts, maxLoad);

        // 获取能运放调整开关的所有可能组合
        CanRunAdjustFeeder canRunAdjust = contactHandleService.getCanRunAdjust(feederNt, canContactFeederKgs, contactFeederNts, nodePath, maxLoad, nodePath.getNodeMap());

        // 单个转供
        List<List<FeederTransferCap>> singleCanFTrs = canRunAdjust.getSingleCanFTrs();

        // 不能单个转供 有问题
        if (CollectionUtils.isEmpty(singleCanFTrs)) {
            return SegmentationEnum.SOMETHING;
        }
        return SegmentationEnum.NO_PROBLEM;
    }
}
