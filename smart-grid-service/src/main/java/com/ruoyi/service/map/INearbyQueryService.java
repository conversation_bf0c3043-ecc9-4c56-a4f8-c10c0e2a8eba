package com.ruoyi.service.map;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.map.DeviceCoords;
import com.ruoyi.entity.map.bo.DeviceDistanceQueryBo;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.bo.NearbyDeviceQueryBo;
import com.ruoyi.entity.map.bo.NearbySubstationQueryBo;
import com.ruoyi.entity.map.vo.*;

import java.io.IOException;
import java.util.List;

/**
 * 附近查询服务接口
 * 统一处理所有最近线路和变电站的查询逻辑
 * 
 * <AUTHOR>
 */
public interface INearbyQueryService {

    /**
     * 查询一条线附近的线
     * 根据num判断，如果是-1则返回所有附近的线（和数据库所有的线路都比对，效率低）
     *
     * @param feederRangeQueryBo 查询条件
     * @return 附近线路列表
     */
    List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo);

    /**
     * 快捷查询附近线路（从数据库最近的线路表查询的）
     *
     * @param feederId 线路ID
     * @param radius   查询半径
     * @return 附近线路列表
     */
    List<DeviceFeeder> selectNearFeeder(String feederId, Double radius);

    /**
     * 根据已有的线路，和线路集合查询最近的
     *
     * @param feederRangeQueryBo 查询条件实体
     * @param deviceFeederList   已知的附近线
     * @return 最近线路列表
     */
    List<DeviceFeeder> selectNeedFeeder(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList);

    /**
     * 点找线，根据一个点去查寻最近的线路的最近杆塔
     *
     * @param doubles          目标点坐标
     * @param num              返回数量
     * @param deviceFeederList 线路列表
     * @return 最近线路信息列表
     * @throws IOException IO异常
     */
    List<NeedFeederVo> pointBy(double[] doubles, Integer num, List<DeviceFeeder> deviceFeederList) throws IOException;

    /**
     * 线找线（通过一条线找另一条线最近的点）
     *
     * @param doubleArrays 目标点坐标集合
     * @param num          最近几条的数量
     * @param filteredList 线路集合
     * @return 最近线路信息列表
     */
    List<List<NeedFeederVo>> pointByParallel(List<double[]> doubleArrays, Integer num, List<DeviceFeeder> filteredList);

    /**
     * 批量处理设备馈线数据，关联运行杆塔和物理杆塔信息
     *
     * @param deviceFeederList 待处理的设备馈线列表
     * @return 关联后的设备坐标信息列表
     */
    List<DeviceCoords> processDeviceFeeders(List<DeviceFeeder> deviceFeederList);

    /**
     * 根据线路ID查询附近变电站信息
     *
     * @param psrId 线路ID
     * @return 附近变电站信息列表
     */
    List<NearbySubstationInfoVo> queryNearbySubstations(String psrId);
    /**
     * 动态分段异步查询所有线路
     *
     * @return 分段的线路列表
     */
    List<List<DeviceFeeder>> getAllPageDeviceFeedersAsync();

    /**
     * 根据馈线ID查询最近的变电站列表
     *
     * @param feederId 馈线ID
     * @param radius 查询半径（公里）
     * @param maxCount 返回的最大变电站数量
     * @param isSwitch false：只返回备用的，true：返回变电站所有的开关
     * @return 最近的变电站信息列表，按距离从近到远排序
     *
     */
    List<NearbySubstationInfoVo> queryNearestSubstationsByFeeder(String feederId, Double radius, Integer maxCount,boolean isSwitch);

    /**
     * 查询附近线路展示信息
     * 根据线路ID和半径查询附近线路的详细信息，包括ID、名称、负载率、是否重过载、所属母线、所属隔离开关
     *
     * @param feederId 线路ID
     * @param radius   查询半径（米）
     * @return 附近线路信息列表
     */
    List<NearbyLineInfoVo> getNearbyLinesInfo(String feederId, Double radius);

    /**
     * 查询附近变电站 调用内网接口实现
     * （ID、名称、剩余间隔、容量）
     * @param feederId 线路ID
     * @param bufferRadius 查询半径（米）
     * @return 附近变电站信息列表
     */
    List<NearbySubstationInfoVo> queryNearbySubstationsByNet(String feederId, double bufferRadius);

    /**
     * 查询附近设备信息
     * 包括物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     *
     * @param feederId 线路ID
     * @param bufferRadius 查询半径（米）
     * @return 附近设备信息列表
     */
    List<NearbyDeviceInfoVo> queryNearbyDevices(String feederId, double bufferRadius);

    /**
     * 根据一个坐标点查询附近物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     * @return 附近设备信息列表，按距离排序
     */
    List<NearbyDeviceInfoVo> queryNearbyDevicesByPoint(NearbyDeviceQueryBo queryBo);

    /**
     * 根据坐标点查询附近变电站
     * @return 附近变电站信息列表，按距离排序
     */
    List<NearbySubstationInfoVo> queryNearbySubstationsByPoint(NearbySubstationQueryBo queryBo);

    /**
     * 计算两个设备坐标的距离信息和B设备的详细信息
     * 包括直线距离、规划路径长度、B设备所属线路、所属主变、装机容量、历史最大电流等
     *
     * @param queryBo 查询参数对象
     * @return 设备距离信息
     */
    DeviceDistanceInfoVo calculateDeviceDistanceInfo(DeviceDistanceQueryBo queryBo);
}
