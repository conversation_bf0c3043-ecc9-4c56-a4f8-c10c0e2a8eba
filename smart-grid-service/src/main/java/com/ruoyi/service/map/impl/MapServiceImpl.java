package com.ruoyi.service.map.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.graphhopper.util.shapes.GHPoint;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.MercatorLineCircleIntersection;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.DeviceCoords;
import com.ruoyi.entity.map.PathSegment;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.map.bo.DeviceDistanceQueryBo;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.TopoTraceReal;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.device.*;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.mapper.znap.BayQueryMapper;
import com.ruoyi.mapper.znap.ConDmsCabinetMapper;
import com.ruoyi.mapper.znap.TpLinkCbMapper;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.service.map.INearbyQueryService;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.generatePlan.BaseGeneratePlan;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.GeoDistanceCalculator;
import com.ruoyi.util.ListUtils;
import com.ruoyi.vo.BusbarSwitchVo;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.util.map.GisUtils;
import com.ruoyi.util.map.PathSegmentationUtil;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.entity.psm.AmapSdkCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.constant.DeviceConstants.*;
import static com.ruoyi.entity.cost.DeviceType.*;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestFeederAllDevice;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestPoints;
import static com.ruoyi.util.map.LineDistanceQuery.selectLine;
import static com.ruoyi.util.map.MapAPICall.extractPolylines;
import static com.ruoyi.util.map.MapAPICall.sendGetRequest;
import static com.ruoyi.util.map.TowerCoordinateGenerator.generateTowerCoordinates;
import static java.lang.Thread.sleep;

@Service
@Slf4j
public class MapServiceImpl implements IMapService {
    @Value("${mapPath.path1}")
    private String path1;

    @Value("${mapPath.path2}")
    private String path2;

    @Resource
    FeederDeviceMapper feederDeviceMapper;

    @Resource
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Resource
    DeviceFeederJkMapper deviceFeederJkMapper;

    @Resource
    DeviceFeederCableMapper deviceFeederCableMapper;

    @Value("${spring.profiles.default}")
    private String activeProfile = "";

    @Resource
    private GisUtils gisUtils;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private ExecutorService executorService; // 注入自定义线程池

    @Resource
    private KgMapper kgMapper; // 注入自定义线程池

    @Autowired
    ISingMapService singMapService;

    @Autowired
    ContactHandleService contactHandleService;

    @Resource
    TpLinkCbMapper tpLinkCbMapper;

    @Resource
    IConditionService iConditionService;

    @Resource
    BaseGeneratePlan baseGeneratePlan;

    @Resource
    IBayQueryService bayQueryService;

    @Resource
    private DeviceSubstation2Mapper deviceSubstation2Mapper;

    @Resource
    private BayQueryMapper bayQueryMapper;

    @Resource
    private ConDmsCabinetMapper conDmsCabinetMapper;

    @Resource
    private INearbyQueryService nearbyQueryService;

    @Value("${bdz-handler.mode}")
    private Boolean bdzHandlerMode;

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
    @Resource
    private DeviceStationBreakerMapper deviceStationBreakerMapper;
    @Resource
    private DeviceWlgtMapper deviceWlgtMapper;


    /**
     * 计算两点最优路劲
     *
     * @param start 起点坐标集合
     * @param end   结束坐标点集合
     * @param token 思级地图token
     * @return
     */
    public List<CalcRouteVo> calculateRouteAsync(List<double[]> start, List<double[]> end, String token) {
        //判断入参是否为空
        if (start == null || end == null || token == null) {
            return null;
        }

        List<CompletableFuture<CalcRouteVo>> futures = new ArrayList<>();
        for (int i = 0; i < start.size(); i++) {
            final int index = i; // 捕获循环索引供 lambda 使用
            // TODO 担心同时调用多个接口会封掉IP 我们这里加延时一下
            try {
                sleep(1);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            futures.add(CompletableFuture.supplyAsync(() -> mapRoute(start.get(index), end.get(index), token)));
        }
        // 等待所有future完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        // 获取所有结果
        List<CalcRouteVo> results = allFutures.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList())).join();
        return results;
    }

    /***
     * 计算路劲方法
     * @param strPoint 起点坐标
     * @param endPoint 重点坐标
     * @param token   思级地图token
     * @return
     */
    @Override
    public CalcRouteVo mapRoute(double[] strPoint, double[] endPoint, String token) {
        //判断入参是否为空
        if (strPoint == null || strPoint.length < 2 || endPoint == null || endPoint.length < 2 || StringUtils.isEmpty(token)) {
            throw new IllegalArgumentException("起点或终点坐标格式错误或者token异常");
        }

        String str = Arrays.toString(strPoint);  // 输出："[1.1, 2.2]"
        String end = Arrays.toString(endPoint);  // 输出："[1.1, 2.2]"
        // 如果需要去掉方括号和空格：
        str = str.replaceAll("[\\[\\]\\s]", "");
        end = end.replaceAll("[\\[\\]\\s]", "");
        String apiUrl = path1 + str + "&" + path2 + end;

        List<Node> nodeList = new ArrayList<>();
        List<double[]> lngLatList = new ArrayList<>();
        Double totalLength = 0.0;

        try {
            // TODO 查询路径规划接口
            RouteVo polylines = new RouteVo();
//            if ("dev".equals(activeProfile)) {
//                //获取spring激活的配置文件
//                String response = sendGetRequest(apiUrl, token);
//                JSONObject root = JSON.parseObject(response);
//                Object codeObj = root.get("code");
//                // 如果出现思级地图访问异常，则直接将两个点变成一个路径生成杆塔
//                if (codeObj != null && !codeObj.equals("200")) {
//                    List<GHPoint> ghPoints = new ArrayList<>();
//                    ghPoints.add(new GHPoint(strPoint[1], strPoint[0]));
//                    ghPoints.add(new GHPoint(endPoint[1], endPoint[0]));
//                    polylines.setRouteList(ghPoints);
//                } else {
//                    //解析路径坐标集合
//                    log.warn("地图API响应为空，生成默认两点一线路径");
//                    polylines = extractPolylines(response);
//                }
//            } else {
//                Map<String, String> parm = new HashMap<>();
//                parm.put("origin", str);
//                parm.put("destination", end);
//                // 解析路径坐标集合
//                String commonGis = gisUtils.commonGis(parm);
//                if (StringUtils.isEmpty(commonGis)) {
                    List<GHPoint> ghPoints = new ArrayList<>();
                    ghPoints.add(new GHPoint(strPoint[1], strPoint[0]));
                    ghPoints.add(new GHPoint(endPoint[1], endPoint[0]));
                    polylines.setRouteList(ghPoints);
//                } else {
//                    polylines = extractPolylines(commonGis);
//                }
//            }

            List<GHPoint> routeList = polylines.getRouteList();
            routeList.forEach(ghPoint -> {
                double[] temp = {ghPoint.getLon(), ghPoint.getLat()};
                lngLatList.add(temp);
            });
            //计算长度
            totalLength = countLength(routeList);
            //分析杆塔
            List<GHPoint> gtList = generateTowerCoordinates(polylines.getRouteList());

            //判断起点和终点是否和生产的路径对应上，如果对不上则手动添加起始点和终点
            double[] routeStrPoint = {polylines.getRouteList().get(0).getLon(), polylines.getRouteList().get(0).getLat()};
            double[] routeEndPoint = {polylines.getRouteList().get(polylines.getRouteList().size() - 1).getLon(), polylines.getRouteList().get(polylines.getRouteList().size() - 1).getLat()};
            if (routeStrPoint[0] != strPoint[0] || routeStrPoint[1] != strPoint[1]) {
                GHPoint point = new GHPoint(strPoint[1], strPoint[0]);
                polylines.getRouteList().add(0, point);
                gtList.add(0, point);
            }
            if (routeEndPoint[0] != endPoint[0] || routeEndPoint[1] != endPoint[1]) {
                GHPoint point = new GHPoint(endPoint[1], endPoint[0]);
                polylines.getRouteList().add(point);
                gtList.add(point);
            }

            // 将路径和杆塔转化成后续node需要的list
            PathSegmentationUtil pathSegmentationUtil = new PathSegmentationUtil();
            List<PathSegment> list = pathSegmentationUtil.segmentRouteByTowers(polylines.getRouteList(), gtList);


            // 生产nodeList
            int count = 0;
            for (PathSegment pathSegment : list) {
                if (count == 0) {
                    Node node1 = createDevice(pathSegment.getStartTower(), "wlgt", "杆塔");
                    nodeList.add(node1);
                    Node node2 = createEdge(pathSegment, "dxd", "架空线");
                    nodeList.add(node2);
                    Node node3 = createDevice(pathSegment.getEndTower(), "wlgt", "杆塔");
                    nodeList.add(node3);
                } else {
                    Node node2 = createEdge(pathSegment, "dxd", "架空线");
                    nodeList.add(node2);
                    Node node3 = createDevice(pathSegment.getEndTower(), "wlgt", "杆塔");
                    nodeList.add(node3);
                }
                count++;
            }
            int nodeCount = 0;
            for (Node node : nodeList) {
                if (node.getPsrType().equals("wlgt")) {
                    if (nodeCount == 0) {
                        node.addEdge(nodeList.get(nodeCount + 1), true);
                        nodeCount = nodeCount + 1;
                        continue;
                    }
                    if (nodeCount == nodeList.size() - 1) {
                        node.addEdge(nodeList.get(nodeCount - 1), false);
                    } else {
                        node.addEdge(nodeList.get(nodeCount + 1), true);
                        node.addEdge(nodeList.get(nodeCount - 1), false);
                    }
                }
                nodeCount = nodeCount + 1;
            }
        } catch (Exception e) {
            return null;
        }
        HashMap<String, Object> objectHashMap = new HashMap<>();

        objectHashMap.put("length", totalLength);
        objectHashMap.put("contactNodeList", nodeList);
        return new CalcRouteVo(totalLength, nodeList, lngLatList);
    }


    /**
     * 查询一条线附近的线,根据num判断，如果是-1则返回所有附近的线（和数据库所有的线路都比对，效率低）
     *
     * @param feederRangeQueryBo
     * @return
     */
    @Override
    public List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo) {
        return nearbyQueryService.feederRangeQuery(feederRangeQueryBo);
    }

    /**
     * 根据杆塔id查询两端导线
     */
    @Override
    public List<WireEndVo> selectWireEnd(String psrId) {
        List<WireEndVo> wireEndVoList = getWireEndVos(psrId);

        if (CollectionUtils.isEmpty(wireEndVoList)) {
            LambdaQueryWrapper<DeviceRunTower> runTowerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            runTowerLambdaQueryWrapper.eq(DeviceRunTower::getAstId, psrId);
            DeviceRunTower deviceRunTower = deviceRunTowerMapper.selectOne(runTowerLambdaQueryWrapper);
            if (deviceRunTower != null) {
                return getWireEndVos(deviceRunTower.getPsrId());
            }
        }
        return wireEndVoList;
    }


    /**
     * id和设备类型判断他的真正psrId
     */
    @Override
    public String selectPsrId(String psrId, String psrType) {
        if (psrType.equals(WLGT)) {
            LambdaQueryWrapper<DeviceRunTower> runTowerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            runTowerLambdaQueryWrapper.eq(DeviceRunTower::getAstId, psrId);
            DeviceRunTower deviceRunTower = deviceRunTowerMapper.selectOne(runTowerLambdaQueryWrapper);
            if (deviceRunTower != null) {
                return deviceRunTower.getPsrId();
            }
            return psrId;
        }
        return null;
    }

    /**
     * 查询运方调整转供路径
     * closeId 联络开关
     * openId  主干开关
     */
    @Override
    public List<NodeVo> transferSupply(String feederId, String closeId, String openId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);

        NodePath nodePath = singAnalysis.getNodePath();

        //找寻联络开关所在的节点分支
        List<Node> closeNodeList = nodePath.getAllNodePaths().stream().filter(path -> path.stream().filter(node -> node.getPsrId() != null) // 跳过psrId为空的节点
                .anyMatch(node -> node.getPsrId().equals(closeId))).findFirst().orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(closeNodeList)) {
            return null;
        }

        //闭合的开关
        Node contactNode = nodePath.getNodeByPsrId(closeId);
        //打开的开关
        Node fenNode = nodePath.getNodeByPsrId(openId);
        //上一个节点
        int index = closeNodeList.indexOf(contactNode) - 1;
        if (index < 0) {
            return null;
        }
        List<Node> nodes = contactHandleService.powerSupplyNodes(contactNode, closeNodeList.get(index), fenNode);
        List<NodeVo> nodeVoList = NodeUtils.toNodeVos(nodes);
        return nodeVoList;
    }

    /**
     * 查询杆塔关联的架空线就或者导线
     *
     * @param psrId
     * @return
     */
    private List<WireEndVo> getWireEndVos(String psrId) {
        //查询杆塔相关联的架空线
        LambdaQueryWrapper<DeviceFeederJk> jkLambdaQueryWrapper = new LambdaQueryWrapper<>();
        jkLambdaQueryWrapper.eq(DeviceFeederJk::getStartPole, psrId);
        jkLambdaQueryWrapper.or();
        jkLambdaQueryWrapper.eq(DeviceFeederJk::getStopPole, psrId);
        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectList(jkLambdaQueryWrapper);

        List<WireEndVo> wireEndVoList = new ArrayList<>();
        //重新封装返回公共实体
        if (CollectionUtils.isNotEmpty(deviceFeederJkList)) {
            for (DeviceFeederJk deviceFeederJk : deviceFeederJkList) {
                wireEndVoList.add(new WireEndVo(deviceFeederJk.getPsrId(), deviceFeederJk.getName(), JK));
            }
        }

        //查询杆塔相关联的导线段
        LambdaQueryWrapper<DeviceFeederCable> cableLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cableLambdaQueryWrapper.eq(DeviceFeederCable::getStartPosition, psrId);
        cableLambdaQueryWrapper.or();
        cableLambdaQueryWrapper.eq(DeviceFeederCable::getEndPosition, psrId);
        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectList(cableLambdaQueryWrapper);
        //重新封装返回公共实体
        if (CollectionUtils.isNotEmpty(deviceFeederCableList)) {
            for (DeviceFeederCable deviceFeederCable : deviceFeederCableList) {
                wireEndVoList.add(new WireEndVo(deviceFeederCable.getPsrId(), deviceFeederCable.getName(), CABLE));
            }
        }
        return wireEndVoList;
    }

    /**
     * 快捷查询附近线路（从是数据库最近的线路表查询的）
     *
     * @param feederId
     * @param radius
     * @return
     */
    public List<DeviceFeeder> selectNearFeeder(String feederId, Double radius) {
        return nearbyQueryService.selectNearFeeder(feederId, radius);
    }

    /**
     * 根据已有的线路，和线路集合查询最近的
     *
     * @param feederRangeQueryBo 查询条件实体
     * @param deviceFeederList   已知的附近线
     * @return
     */
    public List<DeviceFeeder> selectNeedFeeder(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList) {
        return nearbyQueryService.selectNeedFeeder(feederRangeQueryBo, deviceFeederList);
    }


    /**
     * 线找线（通过一条线找另一条线最近的点）
     *
     * @param doubleArrays 目标点坐标集合
     * @param num          最近几条的数量
     * @param filteredList 线路集合
     * @return
     */
    public List<List<NeedFeederVo>> pointByParallel(List<double[]> doubleArrays, Integer num, List<DeviceFeeder> filteredList) {
        return nearbyQueryService.pointByParallel(doubleArrays, num, filteredList);
    }

    /**
     * 计算路径长度
     *
     * @param ghPoints
     * @return
     */
    public static Double countLength(List<GHPoint> ghPoints) {
        // 创建GeometryFactory实例（使用默认精度）
        GeometryFactory factory = new GeometryFactory();

        // 方式2: 使用ArrayList动态批量添加
        List<Coordinate> coordList = new ArrayList<>();
        for (GHPoint ghPoint : ghPoints) {
            Coordinate coordinate = new Coordinate();
            coordinate.setY(ghPoint.getLat());
            coordinate.setX(ghPoint.getLon());
            coordList.add(coordinate);
        }

        Coordinate[] coords = coordList.toArray(new Coordinate[0]);

        // 创建LineString对象
        org.locationtech.jts.geom.LineString lineString = factory.createLineString(coords);
        lineString.setSRID(4326);
        // 计算长度（单位取决于坐标系，例如经纬度坐标系下单位是度，需转换为米）
        double length = haversineLength(lineString);
        return length;
    }

    public static double haversineLength(org.locationtech.jts.geom.LineString line) {
        final double R = 6371000; // 地球半径（米）
        double totalDistance = 0.0;

        for (int i = 0; i < line.getNumPoints() - 1; i++) {
            Coordinate c1 = line.getCoordinateN(i);
            Coordinate c2 = line.getCoordinateN(i + 1);

            double dLat = Math.toRadians(c2.y - c1.y);
            double dLon = Math.toRadians(c2.x - c1.x);
            double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(Math.toRadians(c1.y)) * Math.cos(Math.toRadians(c2.y)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            totalDistance += R * c;
        }
        return totalDistance;
    }

    /**
     * 计算杆塔node
     *
     * @param ghPoint
     * @param type
     * @param name
     * @return
     */
    private static Node createDevice(GHPoint ghPoint, String type, String name) {
        NodeFactory nodeFactory = new NodeFactory();
        Node node = new Node(UUID.randomUUID().toString());
        node.setGeometry(nodeFactory.createDevice(node.getId(), ghPoint.getLon(), ghPoint.getLat()).getGeometry());
        node.setPsrType(type);
        node.setPsrName(name);
        node.setType(Node.TYPE_SELF);
        node.setShapeKey(NodeConstants.SHAPE_KEY_WLGT);

        node.setProperties(new HashMap<String, Object>() {{
            put("type", NodeConstants.SHAPE_KEY_WLGT);
            put("name", name);
        }});
        return node;
    }

    /**
     * 计算线路node
     *
     * @param pathSegment
     * @param type
     * @param name
     * @return
     */
    private static Node createEdge(PathSegment pathSegment, String type, String name) {
        NodeFactory nodeFactory = new NodeFactory();
        Node node = new Node(UUID.randomUUID().toString());
        node.setEdge(true);
        double[][] coords = {{pathSegment.getStartTower().getLon(), pathSegment.getStartTower().getLat()}, {pathSegment.getEndTower().getLon(), pathSegment.getEndTower().getLat()}};
        node.setGeometry(nodeFactory.createEdge(node.getId(), coords).getGeometry());
        node.setPsrType(type);
        node.setPsrName(name);
        node.setType(Node.TYPE_SELF);
        node.setLineType(NodeConstants.LINE_TYPE_LINEAR);
        node.setShapeKey(NodeConstants.SHAPE_KEY_FEEDER_JK);
        node.setProperties(new HashMap<String, Object>() {{
            put("type", NodeConstants.SHAPE_KEY_FEEDER_JK);
            put("name", name);
        }});
        return node;
    }


    /**
     * 获取联络开关信息列表
     * 从拓扑关系中获取联络开关信息，包括联络开关名称、联络线路名称、线路负载率、能否专供等数据
     *
     * @param feederId 线路ID
     * @return 联络开关信息列表
     */
    @Override
    public List<ContactSwitchInfoVo> getContactSwitchInfo(String feederId) {
        if (StringUtils.isEmpty(feederId)) {
            log.warn("输入的线路ID为空");
            return Collections.emptyList();
        }
        try {
            log.info("开始查询联络开关信息，线路ID: {}", feederId);
            // 获取单线图分析结果
            SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
            if (singAnalysis == null) {
                log.warn("未获取到线路{}的单线图分析结果", feederId);
                return Collections.emptyList();
            }
            // 获取拓扑信息
            ZnapTopology topology = singAnalysis.getTopologyMap();
            NodePath nodePath = singAnalysis.getNodePath();
            List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(nodePath);
            //  获取最大负荷
            double maxLoad = contactHandleService.getMaxFeederLoad();
            // 转换联络开关信息
            return buildContactSwitchInfo(nodePath, contactFeederNts, maxLoad);
        } catch (Exception e) {
            log.error("查询联络开关信息失败，线路ID: {}, 异常: {}", feederId, e.getMessage());
            throw new RuntimeException("查询联络开关信息失败", e);
        }
    }

    /**
     * 构建联络开关信息列表
     */
    private List<ContactSwitchInfoVo> buildContactSwitchInfo(NodePath nodePath, List<FeederNtVo> contactFeederNts, double maxLoad) {
        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();
        List<Node> contactKgNodes = nodePath.getContactKgNodes();
        Map<String, Node> contactKgNodeMap = contactKgNodes.stream().collect(Collectors.toMap(Node::getPsrId, d -> d));
        if (CollectionUtils.isEmpty(contactFeederKgs) || CollectionUtils.isEmpty(contactFeederNts)) {
            return Collections.emptyList();
        }
        try {
            // 构建馈线NT映射
            Map<String, FeederNtVo> feederNtMap = contactFeederNts.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, Function.identity(), (v1, v2) -> v1));
            for (ContactFeederKg contactFeederKg : contactFeederKgs) {
                String kgPsrId = contactFeederKg.getKgPsrId();
                Node node = contactKgNodeMap.get(kgPsrId);
                if (node != null) {
                    contactFeederKg.setKgPsrName(node.getPsrName());
                }
                String feederPsrId = contactFeederKg.getFeederPsrId();
                FeederNtVo feederNtVo = feederNtMap.get(feederPsrId);
                if (feederNtVo != null) {
                    contactFeederKg.setFeederPsrName(feederNtVo.getName());
                }
            }
            // 转换联络开关信息
            return contactFeederKgs.stream().map(contactKg -> convertToSwitchInfo(contactKg, feederNtMap.get(contactKg.getFeederPsrId()), maxLoad)).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("构建联络开关信息失败: {}", e.getMessage());
            throw new RuntimeException("构建联络开关信息失败", e);
        }
    }

    /**
     * 转换单个联络开关信息
     */
    private ContactSwitchInfoVo convertToSwitchInfo(ContactFeederKg contactKg, FeederNtVo feederNtVo, double maxLoad) {
        if (contactKg == null || feederNtVo == null) {
            return null;
        }
        try {
            ContactSwitchInfoVo switchInfo = new ContactSwitchInfoVo();
            // 设置开关信息
            switchInfo.setSwitchId(contactKg.getKgPsrId());
            switchInfo.setSwitchName(contactKg.getKgPsrName());
            switchInfo.setSwitchType(contactKg.getKgPsrType());

            // 设置联络线路信息
            switchInfo.setContactLineId(contactKg.getFeederPsrId());
            switchInfo.setContactLineName(contactKg.getFeederPsrName());

            // 设置负荷率
            double loadRate = feederNtVo.getHisMaxLoadRate();
            switchInfo.setLoadRate(feederNtVo.getHisMaxLoadRateStr());
            switchInfo.setCanTransfer(loadRate < maxLoad);

            return switchInfo;
        } catch (Exception e) {
            log.error("转换联络开关信息失败, 开关ID: {}, 异常: {}", contactKg.getKgPsrId(), e.getMessage());
            return null;
        }
    }

    /**
     * 计算基于转供路径的负载率变化
     * 根据转供路径计算当前线路转供至另一条线路的负载率变化
     *
     * @param feederId 源线路ID
     * @param closeId  联络开关ID（需要闭合的开关）
     * @param openId   主干开关ID（需要断开的开关）
     * @return 负载率变化计算结果
     */
    @Override
    public FeederTransferCap calculateLoadRateChange(String feederId, String closeId, String openId) {
        if (StringUtils.isEmpty(feederId) || StringUtils.isEmpty(closeId) || StringUtils.isEmpty(openId)) {
            log.warn("输入参数不能为空: feederId={}, closeId={}, openId={}", feederId, closeId, openId);
            return null;
        }

        try {
            log.info("开始计算负载率变化，源线路ID: {}, 联络开关ID: {}, 主干开关ID: {}", feederId, closeId, openId);

            // 2. 获取单线图分析结果以获取拓扑信息
            SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
            if (singAnalysis == null) {
                log.warn("未获取到线路{}的单线图分析结果", feederId);
                return null;
            }

            NodePath nodePath = singAnalysis.getNodePath();
            List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();

            Node heNode = nodePath.getNodeByPsrId(closeId);
            Node fenNode = nodePath.getNodeByPsrId(openId);

            // =========================== 查找专供路径 =======================
            List<Node> tfrPaths = contactHandleService.powerSupplyNodes(heNode, fenNode, nodePath);
            FeederNtVo mainFeederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);
            List<Node> pbList = tfrPaths.stream().filter(Node::isPb).collect(Collectors.toList());

            // 配变节点的容量加工
            baseGeneratePlan.processPbNodeCap(pbList);

            // =========================== 查找关联联络线路 =======================
            // 查找合闸开关关联的联络线
            ContactFeederKg contactFeederKg = ListUtils.findFirst(contactFeederKgs, (d) -> StringUtils.equals(d.getKgPsrId(), heNode.getPsrId()));
            FeederNtVo contactFeederNt = feederDeviceMapper.selectFeederNtsByFeederId(contactFeederKg.getFeederPsrId());

            // =========================== 计算转供负载率 =======================
            return contactHandleService.calcFeederTransferCap(mainFeederNt, contactFeederNt, pbList);

        } catch (Exception e) {
            log.error("计算负载率变化失败，源线路ID: {}, 联络开关ID: {}, 主干开关ID: {}, 异常: {}", feederId, closeId, openId, e.getMessage(), e);
            throw new RuntimeException("计算负载率变化失败: " + e.getMessage(), e);
        }
    }



}
