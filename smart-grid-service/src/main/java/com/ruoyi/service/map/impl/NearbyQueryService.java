package com.ruoyi.service.map.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.MercatorLineCircleIntersection;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.DeviceCoords;
import com.ruoyi.entity.map.NearFeeder;
import com.ruoyi.entity.map.bo.DeviceDistanceQueryBo;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.bo.NearbyDeviceQueryBo;
import com.ruoyi.entity.map.bo.NearbySubstationQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.entity.psm.AmapSdkCommon;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.entity.znap.TopoTraceReal;
import com.ruoyi.mapper.device.*;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.mapper.znap.BayQueryMapper;
import com.ruoyi.service.map.INearbyQueryService;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.util.GeoDistanceCalculator;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.util.map.LineDistanceQuery;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.constant.DeviceConstants.HWG_TYPES;
import static com.ruoyi.constant.DeviceConstants.KGZ_TYPES;
import static com.ruoyi.constant.DeviceConstants.POLE_TYPES;
import static com.ruoyi.entity.cost.DeviceType.WLGT;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestFeederAllDevice;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestPoints;
import static com.ruoyi.util.map.LineDistanceQuery.selectLine;

/**
 * 附近查询服务实现类
 * 统一处理所有最近线路和变电站的查询逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NearbyQueryService implements INearbyQueryService {

    @Resource
    private FeederDeviceMapper feederDeviceMapper;

    @Resource
    private NearFeederMapper nearFeederMapper;

    @Resource
    private KgMapper kgMapper;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private ExecutorService executorService;

    @Resource
    private DeviceSubstation2Mapper deviceSubstation2Mapper;

    @Resource
    private BayQueryMapper bayQueryMapper;

    @Autowired
    private IBayQueryService bayQueryService;

    @Autowired
    private IMapService mapService;

    @Value("${bdz-handler.mode}")
    private Boolean bdzHandlerMode;

    @Autowired
    ContactHandleService contactHandleService;


    private final ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    /**
     * 查询一条线附近的线,根据num判断，如果是-1则返回所有附近的线（和数据库所有的线路都比对，效率低）
     *
     * @param feederRangeQueryBo 查询条件
     * @return 附近线路列表
     */
    @Override
    public List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo) {
        // 目标线路
        List<List<DeviceFeeder>> deviceFeederList = getAllPageDeviceFeedersAsync();

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }
        List<CompletableFuture<List<DeviceFeeder>>> futures = new ArrayList<>();

        if (CollectionUtils.isEmpty(feederRangeQueryBo.getCoordinateList())) {
            LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederRangeQueryBo.getPsrId());

            String geoList = feederDeviceMapper.selectOne(lambdaQueryWrapper).getGeoList();
            if (StringUtils.isBlank(geoList)) {
                return null;
            }

            feederRangeQueryBo.setCoordinateList(CoordinateConverter.split(geoList));
        }
        List<NeedFeederVo> needFeederVos = new ArrayList<>();
        for (int i = 0; i < deviceFeederList.size(); i++) {
            int finalI = i;

            futures.add(CompletableFuture.supplyAsync(() -> selectLine(feederRangeQueryBo, deviceFeederList.get(finalI), needFeederVos)));
        }
        List<DeviceFeeder> feederList = futures.stream().map(CompletableFuture::join).flatMap(List::stream).collect(Collectors.toList());

        if (feederRangeQueryBo.getNum() == -1) {
            selectLine(feederRangeQueryBo, distinctByPsrId(feederList), needFeederVos);
            return distinctByPsrIdVo(needFeederVos);
        }

        return distinctByPsrIdVo(needFeederVos);
    }

    /**
     * 快捷查询附近线路（从数据库最近的线路表查询的）
     *
     * @param feederId 线路ID
     * @param radius   查询半径
     * @return 附近线路列表
     */
    @Override
    public List<DeviceFeeder> selectNearFeeder(String feederId, Double radius) {
        LambdaQueryWrapper<NearFeeder> nearFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        nearFeederLambdaQueryWrapper.eq(NearFeeder::getFeederId, feederId);
        NearFeeder nearFeeder = nearFeederMapper.selectOne(nearFeederLambdaQueryWrapper);

        if (nearFeeder == null) {
            return null;
        }

        List<String> idList = Arrays.stream(nearFeeder.getNearFeederId().split(",")).filter(s -> !s.isEmpty()) // 过滤空字符串
                .map(String::trim) // 去除首尾空格
                .collect(Collectors.toList());

        LambdaQueryWrapper<DeviceFeeder> deviceFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceFeederLambdaQueryWrapper.in(DeviceFeeder::getPsrId, idList);
        List<DeviceFeeder> deviceFeederList = feederDeviceMapper.selectList(deviceFeederLambdaQueryWrapper);

        FeederRangeQueryBo feederRangeQueryBo = new FeederRangeQueryBo();
        feederRangeQueryBo.setPsrId(feederId);
        feederRangeQueryBo.setRange(radius);
        feederRangeQueryBo.setNum(-1);

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }
        return selectNeedFeeder(feederRangeQueryBo, deviceFeederList);
    }

    /**
     * 根据已有的线路，和线路集合查询最近的
     *
     * @param feederRangeQueryBo 查询条件实体
     * @param deviceFeederList   已知的附近线
     * @return 最近线路列表
     */
    @Override
    public List<DeviceFeeder> selectNeedFeeder(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList) {
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederRangeQueryBo.getPsrId());
        feederRangeQueryBo.setCoordinateList(CoordinateConverter.split(feederDeviceMapper.selectOne(lambdaQueryWrapper).getGeoList()));
        List<NeedFeederVo> needFeederVos = new ArrayList<>();
        return selectLine(feederRangeQueryBo, deviceFeederList, needFeederVos);
    }

    /**
     * 线找线（通过一条线找另一条线最近的点）
     *
     * @param doubleArrays 目标点坐标集合
     * @param num          最近几条的数量
     * @param filteredList 线路集合
     * @return 最近线路信息列表
     */
    @Override
    public List<List<NeedFeederVo>> pointByParallel(List<double[]> doubleArrays, Integer num, List<DeviceFeeder> filteredList) {
        // 为每个 double [] 数组创建一个任务
        if (filteredList == null) {
            filteredList = feederDeviceMapper.selectList();
        }
        List<CompletableFuture<List<NeedFeederVo>>> futures = new ArrayList<>();

        for (int i = 0; i < doubleArrays.size(); i++) {
            final int index = i; // 捕获循环索引供 lambda 使用

            // 提交任务到线程池
            List<DeviceFeeder> finalFilteredList = filteredList;
            futures.add(CompletableFuture.supplyAsync(() -> {
                double[] array = doubleArrays.get(index);
                try {
                    return pointBy(array, num, finalFilteredList);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }, executor));
        }

        // 保持结果的嵌套结构
        return futures.stream().map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("任务执行失败", e);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 动态分段异步查询所有线路
     *
     * @return 分段的线路列表
     */
    @Override
    public List<List<DeviceFeeder>> getAllPageDeviceFeedersAsync() {
        // 1. 先查询总记录数
        int totalCount = getTotalCount();

        // 2. 根据总记录数和每段大小计算分段数
        int batchSize = 1000; // 每批查询数量
        int batches = (int) Math.ceil((double) totalCount / batchSize);

        // 3. 创建异步任务列表
        List<CompletableFuture<List<DeviceFeeder>>> futures = new ArrayList<>();
        for (int i = 0; i < batches; i++) {
            final int offset = i * batchSize;
            futures.add(CompletableFuture.supplyAsync(() -> queryRange(offset, batchSize), executorService));
        }

        return futures.stream().map(CompletableFuture::join)  // 将每个 future 转换为 List<DeviceFeeder>
                .collect(Collectors.toList());
    }

    /**
     * 配合动态分段异步查询所有线路——查询总记录数
     *
     * @return 总记录数
     */
    private int getTotalCount() {
        return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM device_feeder", Integer.class);
    }

    /**
     * 配合动态分段异步查询所有线路——分页查询方法
     *
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 线路列表
     */
    private List<DeviceFeeder> queryRange(int offset, int limit) {
        String sql = "SELECT * FROM device_feeder LIMIT ? OFFSET ?";
        return jdbcTemplate.query(sql, new Object[]{limit, offset}, new BeanPropertyRowMapper<>(DeviceFeeder.class));
    }

    /**
     * 按照线路id对线路list去重
     *
     * @param feederList 线路列表
     * @return 去重后的线路列表
     */
    private List<NeedFeederVo> distinctByPsrIdVo(List<NeedFeederVo> feederList) {
        if (feederList == null || feederList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenPsrIds = new HashSet<>();
        return feederList.stream().filter(feeder -> seenPsrIds.add(feeder.getPsrId())).collect(Collectors.toList());
    }

    /**
     * 按照线路id对线路list去重
     *
     * @param feederList 线路列表
     * @return 去重后的线路列表
     */
    private List<DeviceFeeder> distinctByPsrId(List<DeviceFeeder> feederList) {
        if (feederList == null || feederList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenPsrIds = new HashSet<>();
        return feederList.stream().filter(feeder -> seenPsrIds.add(feeder.getPsrId())).collect(Collectors.toList());
    }

    /**
     * 点找线，根据一个点去查寻最近的线路的最近杆塔
     *
     * @param doubles          目标点坐标
     * @param num              返回数量
     * @param deviceFeederList 线路列表
     * @return 最近线路信息列表
     * @throws IOException IO异常
     */
    @Override
    public List<NeedFeederVo> pointBy(double[] doubles, Integer num, List<DeviceFeeder> deviceFeederList) throws IOException {

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }

        //先查询所有线上所有设备集合（目前只查询了物理杆塔和间隔）
        //查询运行杆塔
        List<DeviceCoords> deviceCoords = new ArrayList<>();
        List<DeviceCoords> wlgtDeviceCoords = processDeviceFeeders(deviceFeederList);
        deviceCoords.addAll(wlgtDeviceCoords);
        //查询所有间隔开关站内
        List<StationKg> stationKgs = kgMapper.selectFeederKg(deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));
        //站内的开关要符合特殊类型站房才可以使用
        for (StationKg stationKg : stationKgs) {
            if (DeviceConstants.CONTACT_STATION_TYPES.contains(stationKg.getPsrType())) {
                deviceCoords.add(new DeviceCoords(stationKg.getPsrId(), stationKg.getPsrType(), stationKg.getName(), null, stationKg.getLongitude() + "," + stationKg.getLatitude(), stationKg.getFeeder()));
            }
        }

        // 直接去重获取线路信息
        List<String> feedIdList = deviceCoords.stream().map(DeviceCoords::getFeederId) // 提取feederId
                .filter(Objects::nonNull) // 过滤null值（可选）
                .distinct() // 去重
                .collect(Collectors.toList());
        List<DeviceFeeder> deviceFeeders = feederDeviceMapper.selectBatchIds(feedIdList);
        // 构建psrId到name的映射（键：psrId，值：name）
        Map<String, String> psrIdToNameMap = new HashMap<>();
        for (DeviceFeeder feeder : deviceFeeders) {
            psrIdToNameMap.put(feeder.getPsrId(), feeder.getName());
        }
        // 遍历设备坐标列表，匹配feederId与psrId，设置feederName
        for (DeviceCoords coords : deviceCoords) {
            String feederId = coords.getFeederId();
            // 从映射表中查找对应的name（若不存在，返回null或默认值）
            String feederName = psrIdToNameMap.get(feederId);
            coords.setFeederName(feederName); // 设置feederName
        }
        //查询最近的线路
        List<DeviceCoords> nearFeederdeviceCoordsList = findNearestPoints(deviceCoords, doubles, num);
        //查询最近的线上所有设备集合
        Map<String, List<DeviceCoords>> map = findNearestFeederAllDevice(nearFeederdeviceCoordsList, deviceCoords);

        List<NeedFeederVo> result = new ArrayList<>();

        for (DeviceCoords coords : nearFeederdeviceCoordsList) {
            NeedFeederVo feeder = new NeedFeederVo();
            feeder.setPsrId(coords.getFeederId());
            feeder.setFeedName(coords.getFeederName());
            String[] parts = coords.getCoords().split(",");
            feeder.setRecentlyPoint(new double[]{Double.parseDouble(parts[0].trim()),  // 经度
                    Double.parseDouble(parts[1].trim())   // 纬度
            });
            feeder.setDeviceId(coords.getId());
            feeder.setDeviceType(coords.getType());
            feeder.setDeviceName(coords.getName());
            feeder.setDeviceCoordsList(map.get(coords.getFeederId()));
            result.add(feeder);
        }
        return result;
    }

    /**
     * 批量处理设备馈线数据，关联运行杆塔和物理杆塔信息
     *
     * @param deviceFeederList 待处理的设备馈线列表
     * @return 关联后的设备坐标信息列表
     */
    @Override
    public List<DeviceCoords> processDeviceFeeders(List<DeviceFeeder> deviceFeederList) {
        // 存储最终生成的设备坐标信息
        List<DeviceCoords> deviceCoords = new ArrayList<>();

        // 快速映射表：通过astId查找对应的运行杆塔对象
        // 用于后续快速获取物理杆塔对应的运行杆塔名称和馈线ID
        Map<String, DeviceRunTower> astIdToRunTowerMap = new HashMap<>();

        // 1. 批量收集所有馈线ID，准备一次性查询所有相关运行杆塔
        List<String> allPsrIds = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());

        // 2. 批量查询所有运行杆塔数据（减少数据库交互次数）
        List<DeviceRunTower> allRunTowers = feederDeviceMapper.selectRunTowers(allPsrIds);

        // 3. 构建astId到运行杆塔对象的映射关系
        // 目的：后续通过物理杆塔的astId快速找到对应的运行杆塔信息
        for (DeviceRunTower tower : allRunTowers) {
            astIdToRunTowerMap.put(tower.getAstId(), tower);
        }

        // 4. 收集所有运行杆塔关联的物理杆塔ID
        List<String> allAstIds = allRunTowers.stream().map(DeviceRunTower::getAstId).collect(Collectors.toList());

        // 5. 批量查询所有物理杆塔数据
        List<DeviceWlgt> allWlgts = feederDeviceMapper.selectwlgtList(allAstIds);

        // 6. 组装最终结果：将物理杆塔与运行杆塔信息关联
        for (DeviceWlgt wlgt : allWlgts) {
            // 通过astId查找对应的运行杆塔对象
            DeviceRunTower runTower = astIdToRunTowerMap.get(wlgt.getAstId());

            // 防御性检查：确保运行杆塔存在（理论上应该存在，因为数据是关联查询的）
            if (runTower != null) {
                // 注意：名称字段从运行杆塔对象获取（物理杆塔自身没有名称字段）
                deviceCoords.add(new DeviceCoords(wlgt.getAstId(),              // 设备ID
                        WLGT,                         // 设备类型常量
                        runTower.getName(),     // 设备名称（来自运行杆塔）
                        null,     // 距离
                        wlgt.getGeoPositon(),         // 地理位置坐标
                        runTower.getFeeder()           // 所属馈线ID
                ));
            }
        }

        return deviceCoords;
    }

    /**
     * 根据线路ID查询附近变电站信息
     *
     * @param psrId 线路ID
     * @return 附近变电站信息列表
     */
    @Override
    public List<NearbySubstationInfoVo> queryNearbySubstations(String psrId) {
        try {
            log.info("查询附近变电站信息，psrId: {}", psrId);
            // 查询所有变电站
            List<DeviceSubstation> deviceSubstations = deviceSubstation2Mapper.selectList();
            // 查询变电站的剩余间隔
            List<NearbySubstationInfoVo> nearbySubstations = new ArrayList<>();
            for (DeviceSubstation deviceSubstation : deviceSubstations) {
                NearbySubstationInfoVo substationInfoVo = new NearbySubstationInfoVo();
                BeanUtils.copyProperties(deviceSubstation, substationInfoVo);
                List<TopoTraceReal> topoTraceReals = bayQueryMapper.selectSwitchBySubstation(deviceSubstation.getEmsId());
                long remainingBayCount = topoTraceReals.stream().filter(t -> t.getStartBreakName().contains("备用") || t.getStartBreakName().contains("预留")).count();
                //  查询剩余间隔
                substationInfoVo.setRemainingBayCount((int) remainingBayCount);
                nearbySubstations.add(substationInfoVo);
            }

            return nearbySubstations;
        } catch (Exception e) {
            log.error("查询附近变电站信息失败，psrId: {}", psrId, e);
            throw new RuntimeException("查询附近变电站信息失败", e);
        }
    }


    /**
     * 获取馈线所属的母线ID
     *
     * @param feederId 馈线ID
     * @return 母线ID，如果未找到返回null
     */
    private String getFeederBusbarId(String feederId) {
        try {
            BusbarSwitchVo busbarSwitchVo = bayQueryService.queryLineTopology(feederId);
            if (busbarSwitchVo != null) {
                return busbarSwitchVo.getBusbarId();
            }
            log.debug("未找到馈线{}的母线信息", feederId);
            return null;
        } catch (Exception e) {
            log.warn("查询馈线{}母线信息失败: {}", feederId, e.getMessage());
            return null;
        }
    }

    /**
     * 获取变电站的剩余间隔数量
     *
     * @param emsId 变电站EMS ID
     * @return 剩余间隔数量
     */
    private List<TopoTraceReal> getRemainingBayCount(String emsId) {
        try {
            if (StringUtils.isEmpty(emsId)) {
                return new ArrayList<>();
            }
            List<TopoTraceReal> topoTraceReals = bayQueryMapper.selectSwitchBySubstation(emsId);
            if (CollectionUtils.isEmpty(topoTraceReals)) {
                return new ArrayList<>();
            }
            return topoTraceReals;
        } catch (Exception e) {
            log.warn("查询变电站{}剩余间隔失败: {}", emsId, e.getMessage());
            return new ArrayList<>();
        }
    }


    /**
     * 检查变电站是否与馈线在同一母线下
     *
     * @param substation     变电站
     * @param feederBusbarId 馈线所属母线ID
     * @return 如果在同一母线下返回true
     */
    private boolean isSameBusbar(DeviceSubstation substation, String feederBusbarId) {
        try {
            if (StringUtils.isEmpty(feederBusbarId) || StringUtils.isEmpty(substation.getEmsId())) {
                return false;
            }
            // 查询变电站下的所有母线
            List<TopoTraceReal> substationBusbars = bayQueryMapper.selectBusbarsBySubstation(substation.getEmsId());
            if (CollectionUtils.isEmpty(substationBusbars)) {
                return false;
            }
            // 检查是否包含相同的母线ID
            return substationBusbars.stream().anyMatch(busbar -> feederBusbarId.equals(busbar.getBusbar()));
        } catch (Exception e) {
            log.warn("检查变电站{}与馈线母线关系失败: {}", substation.getName(), e.getMessage());
            return false;
        }
    }

    /**
     * 内部类：变电站与距离信息
     */
    @Getter
    private static class SubstationWithDistance {
        private final DeviceSubstation substation;
        private final double distance;
        private List<TopoTraceReal> topoTraceReals;

        public SubstationWithDistance(DeviceSubstation substation, double distance, List<TopoTraceReal> topoTraceReals) {
            this.substation = substation;
            this.distance = distance;
            this.topoTraceReals = topoTraceReals;
        }

    }


    /**
     * 根据馈线ID查询最近的变电站列表
     *
     * @param feederId 馈线ID
     * @param radius   查询半径（公里）
     * @param maxCount 返回的最大变电站数量  -1 返回所有，其他正数传多少最大返回多少
     * @return
     * @parm isSwitch false：只返回备用的，true：返回变电站所有的开关
     */
    @Override
    public List<NearbySubstationInfoVo> queryNearestSubstationsByFeeder(String feederId, Double radius, Integer maxCount, boolean isSwitch) {
        try {
            log.info("根据馈线ID查询最近的变电站列表，feederId: {}, radius: {}公里, maxCount: {}", feederId, radius, maxCount);
            // 获取馈线的所有线段
            LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
            DeviceFeeder feeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

            if (feeder == null || StringUtils.isEmpty(feeder.getGeoList())) {
                log.warn("未找到馈线{}的地理坐标", feederId);
                return new ArrayList<>();
            }

            // 解析馈线的所有线段坐标
            List<List<double[]>> feederLineSegments = CoordinateConverter.split(feeder.getGeoList());
            if (CollectionUtils.isEmpty(feederLineSegments)) {
                log.warn("馈线{}坐标解析失败", feederId);
                return new ArrayList<>();
            }
            // 获取馈线所属母线ID（用于过滤同一母线下的变电站）
            String feederBusbarId = getFeederBusbarId(feederId);

            // 查询所有变电站
            List<DeviceSubstation> allSubstations = deviceSubstation2Mapper.selectList();
            if (CollectionUtils.isEmpty(allSubstations)) {
                log.warn("未找到任何变电站数据");
                return new ArrayList<>();
            }

            // 收集所有有效变电站的坐标点
            List<double[]> substationPoints = new ArrayList<>();
            Map<String, DeviceSubstation> pointToSubstationMap = new HashMap<>();

            Map<String, List<TopoTraceReal>> remainingBayCountMap = new HashMap<>();

            for (DeviceSubstation substation : allSubstations) {
                try {
                    if (StringUtils.isEmpty(substation.getGeoPositon())) {
                        continue;
                    }
                    String[] coords = substation.getGeoPositon().split(",");
                    if (coords.length != 2) {
                        continue;
                    }
                    double substationLon = Double.parseDouble(coords[0].trim());
                    double substationLat = Double.parseDouble(coords[1].trim());
                    // 检查剩余间隔数量
                    List<TopoTraceReal> topoTraceReals = getRemainingBayCount(substation.getEmsId());
                    double[] point = new double[]{substationLon, substationLat};
                    if (isSwitch) {
                        substationPoints.add(point);
                        pointToSubstationMap.put(substationLon + "," + substationLat, substation);
                        remainingBayCountMap.put(substation.getEmsId(), topoTraceReals);
                        continue;
                    }
                    List<TopoTraceReal> traceRealsRemainingBay = topoTraceReals.stream().filter(t -> StringUtils.isNotBlank(t.getStartBreakName()) && (t.getStartBreakName().contains("备用") || t.getStartBreakName().contains("预留"))).collect(Collectors.toList());
                    long remainingBayCount = traceRealsRemainingBay.size();
                    if (remainingBayCount <= 0) {
                        continue;
                    }
                    // 检查是否与当前线路在同一母线下
                    if (isSameBusbar(substation, feederBusbarId)) {
                        continue;
                    }
                    substationPoints.add(point);
                    pointToSubstationMap.put(substationLon + "," + substationLat, substation);
                    remainingBayCountMap.put(substation.getEmsId(), traceRealsRemainingBay);
                } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
                    log.warn("变电站{}坐标格式错误: {}", substation.getName(), substation.getGeoPositon());
                }
            }

            if (substationPoints.isEmpty()) {
                log.info("未找到符合条件的变电站");
                return new ArrayList<>();
            }

            List<SubstationWithDistance> candidateSubstations = new ArrayList<>();

            for (double[] substationPoint : substationPoints) {
                // 计算变电站到馈线所有线段的最小距离
                double minDistance = LineDistanceQuery.calculateMinDistanceToLine(substationPoint, feederLineSegments);
                // 检查是否在半径范围内
                if (radius != null && minDistance > radius) {
                    continue;
                }
                String key = substationPoint[0] + "," + substationPoint[1];
                DeviceSubstation substation = pointToSubstationMap.get(key);
                if (substation != null) {
                    List<TopoTraceReal> topoTraceReals = remainingBayCountMap.get(substation.getEmsId());
                    candidateSubstations.add(new SubstationWithDistance(substation, minDistance, topoTraceReals));
                }
            }
            // 按距离排序
            candidateSubstations.sort(Comparator.comparingDouble(SubstationWithDistance::getDistance));
            // 如果等于-1 不做限制
            if (maxCount != null && maxCount == -1) {
                maxCount = candidateSubstations.size();
            }
            // 限制返回数量
            if (maxCount != null && maxCount > 0 && maxCount < candidateSubstations.size()) {
                candidateSubstations = candidateSubstations.subList(0, maxCount);
            }
            // 结果对象
            List<NearbySubstationInfoVo> result = new ArrayList<>();

            for (SubstationWithDistance substationWithDistance : candidateSubstations) {
                DeviceSubstation substation = substationWithDistance.getSubstation();
                NearbySubstationInfoVo substationInfoVo = new NearbySubstationInfoVo();
                BeanUtils.copyProperties(substation, substationInfoVo);
                substationInfoVo.setDistance(substationWithDistance.getDistance());
                substationInfoVo.setRemainingBayCount(substationWithDistance.getTopoTraceReals().size());
                List<BusbarSwitchVo> busbarSwitchVoList = getBusbarSwitchVos(substationWithDistance, substation);
                substationInfoVo.setBusbarSwitchVoList(busbarSwitchVoList);
                result.add(substationInfoVo);
            }
            log.info("成功查询到{}个符合条件的变电站", result.size());
            return result;
        } catch (Exception e) {
            log.error("根据馈线ID查询最近变电站失败，feederId: {}, radius: {}, maxCount: {}", feederId, radius, maxCount, e);
            throw new RuntimeException("查询最近变电站失败", e);
        }
    }

    private static List<BusbarSwitchVo> getBusbarSwitchVos(SubstationWithDistance substationWithDistance, DeviceSubstation substation) {
        List<TopoTraceReal> topoTraceReals = substationWithDistance.getTopoTraceReals();
        List<BusbarSwitchVo> busbarSwitchVoList = new ArrayList<>();
        for (TopoTraceReal topoTraceReal : topoTraceReals) {
            BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
            busbarSwitchVo.setStationPsrId(substation.getEmsId());
            busbarSwitchVo.setStationPsrName(substation.getName());
            busbarSwitchVo.setBusbarId(topoTraceReal.getBusbar());
            busbarSwitchVo.setBusbarName(topoTraceReal.getBusbarName());
            busbarSwitchVo.setSwitchId(topoTraceReal.getStartBreak());
            busbarSwitchVo.setSwitchName(topoTraceReal.getStartBreakName());
            busbarSwitchVo.setIsSpare(true);
            busbarSwitchVoList.add(busbarSwitchVo);
        }
        return busbarSwitchVoList;
    }

    /**
     * 查询附近线路展示信息
     * 根据线路ID和半径查询附近线路的详细信息，包括ID、名称、负载率、是否重过载、所属母线、所属隔离开关
     *
     * @param feederId 线路ID
     * @param radius   查询半径（米）
     * @return 附近线路信息列表
     */
    @Override
    public List<NearbyLineInfoVo> getNearbyLinesInfo(String feederId, Double radius) {
        if (StringUtils.isEmpty(feederId)) {
            log.warn("线路ID不能为空");
            return new ArrayList<>();
        }

        if (radius == null || radius <= 0) {
            log.warn("查询半径必须大于0");
            return new ArrayList<>();
        }

        try {
            log.info("开始查询附近线路信息，线路ID: {}, 半径: {}米", feederId, radius);
            FeederRangeQueryBo queryBo = new FeederRangeQueryBo();
            queryBo.setPsrId(feederId);
            queryBo.setRange(radius);
            queryBo.setNum(-1); // 返回所有附近线路
            // 记录耗时时间
            long startTime = System.currentTimeMillis();
            // 查询附近线路
            List<NeedFeederVo> nearbyFeeders = feederRangeQuery(queryBo);
            log.info("查询附近线路耗时: {}ms", System.currentTimeMillis() - startTime);

            if (CollectionUtils.isEmpty(nearbyFeeders)) {
                log.info("未找到线路{}半径{}米内的附近线路", feederId, radius);
                return new ArrayList<>();
            }

            // 提取线路ID列表
            List<String> feederIds = nearbyFeeders.stream().map(NeedFeederVo::getPsrId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(feederIds)) {
                log.warn("附近线路ID列表为空");
                return new ArrayList<>();
            }

            // 查询线路负载率信息
            List<FeederNtVo> feederNtList = feederDeviceMapper.selectFeederNtsByFeederIds(feederIds);
            Map<String, FeederNtVo> feederNtMap = feederNtList.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, Function.identity(), (existing, replacement) -> existing));

            // 获取重过载阈值
            double maxLoadRate = contactHandleService.getMaxFeederLoad();

            // 构建结果列表
            List<NearbyLineInfoVo> result = new ArrayList<>();
            for (NeedFeederVo needFeeder : nearbyFeeders) {
                try {
                    NearbyLineInfoVo lineInfo = buildNearbyLineInfo(needFeeder, feederNtMap, maxLoadRate);
                    if (lineInfo != null) {
                        result.add(lineInfo);
                    }
                } catch (Exception e) {
                    log.error("线路{}的附近线路信息失败: {}", needFeeder.getPsrId(), e.getMessage(), e);
                }
            }

            log.info("成功查询到{}条附近线路信息", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询附近线路信息失败，线路ID: {}, 半径: {}, 异常: {}", feederId, radius, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建单个附近线路信息
     *
     * @param needFeeder  附近线路基础信息
     * @param feederNtMap 线路负载率信息映射
     * @param maxLoadRate 重过载阈值
     * @return 附近线路信息VO
     */
    private NearbyLineInfoVo buildNearbyLineInfo(NeedFeederVo needFeeder, Map<String, FeederNtVo> feederNtMap, double maxLoadRate) {
        if (needFeeder == null || StringUtils.isEmpty(needFeeder.getPsrId())) {
            return null;
        }
        try {
            NearbyLineInfoVo lineInfo = new NearbyLineInfoVo();
            // 设置基础信息
            lineInfo.setFeederId(needFeeder.getPsrId());
            lineInfo.setFeederName(needFeeder.getFeedName());
            lineInfo.setSupplyRadius(needFeeder.getSupplyRadius());
            // 设置负载率信息
            FeederNtVo feederNt = feederNtMap.get(needFeeder.getPsrId());
            if (feederNt != null) {
                double loadRateValue = feederNt.getHisMaxLoadRate();
                lineInfo.setLoadRateValue(loadRateValue);
                lineInfo.setLoadRate(feederNt.getHisMaxLoadRateStr());
                lineInfo.setIsOverload(loadRateValue >= maxLoadRate);
            } else {
                lineInfo.setLoadRateValue(0.0);
                lineInfo.setLoadRate("0.0");
                lineInfo.setIsOverload(false);
                log.warn("线路{}未找到负载率信息", needFeeder.getPsrId());
            }
            // 查询母线和隔离开关信息
            BusbarSwitchVo busbarSwitch = bayQueryService.queryLineTopology(needFeeder.getPsrId());
            if (busbarSwitch != null) {
                lineInfo.setBusbarId(busbarSwitch.getBusbarId());
                lineInfo.setBusbarName(busbarSwitch.getBusbarName());
                lineInfo.setSwitchId(busbarSwitch.getSwitchId());
                lineInfo.setSwitchName(busbarSwitch.getSwitchName());
                lineInfo.setSubstationId(busbarSwitch.getStationPsrId());
                lineInfo.setIsSpare(busbarSwitch.getIsSpare());
                lineInfo.setSubstationName(busbarSwitch.getStationPsrName());
            } else {
                log.warn("线路{}未找到母线和开关信息", needFeeder.getPsrId());
            }
            return lineInfo;
        } catch (Exception e) {
            log.error("构建线路{}信息失败: {}", needFeeder.getPsrId(), e.getMessage(), e);
            return null;
        }
    }


    @Override
    public List<NearbySubstationInfoVo> queryNearbySubstationsByNet(String feederId, double bufferRadius) {
        try {
            // 区分内网和外网  外网是模拟接口 参数不一样
            AmapSdkCommon.PSRByPolygonRspModel substationResult;
            if (bdzHandlerMode) {
                log.info("通过内网接口查询附近变电站信息，feederId: {}", feederId);
                // 获取坐标
                LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
                DeviceFeeder feeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
                if (feeder == null || StringUtils.isEmpty(feeder.getGeoList())) {
                    log.warn("未找到馈线{}的地理坐标", feederId);
                    return new ArrayList<>();
                }
                List<List<double[]>> feederLineSegments = CoordinateConverter.split(feeder.getGeoList());
                if (CollectionUtils.isEmpty(feederLineSegments)) {
                    log.warn("馈线{}坐标解析失败", feederId);
                    return new ArrayList<>();
                }
                // 创建缓冲区多边形
                List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(feederLineSegments, bufferRadius);
                // 判断首尾经纬度是否一致，不一致将头添加到尾部
                if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                    bufferPolygon.add(bufferPolygon.get(0));
                }
                // 转为墨卡托坐标
                String polygonMercator = BufferPolygonCreator.convertToMercator(bufferPolygon);
                // 查询附近变电站
                substationResult = querySubstationsByPolygon(polygonMercator);
            } else {
                log.info("外网模拟接口未查询到变电站数据");
                substationResult = querySubstationsByPolygonMock(feederId);
            }

            if (substationResult.getResult() == null || CollectionUtils.isEmpty(substationResult.getResult().getPsrDataList())) {
                log.info("内网接口未查询到变电站数据");
                return new ArrayList<>();
            }

            List<NearbySubstationInfoVo> result = processSubstationData(substationResult, feederId);
            log.info("通过内网接口成功查询到{}个变电站", result.size());
            return result;
        } catch (Exception e) {
            log.error("通过内网接口查询附近变电站失败，feederId: {}", feederId, e);
            return queryNearbySubstations(feederId);
        }
    }

    private AmapSdkCommon.PSRByPolygonRspModel querySubstationsByPolygonMock(String feederId) throws IOException {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("feederId", feederId);
        requestBody.put("type", "BDZ");
        return AmapSdkCommon.queryPSRByPolygon(requestBody);
    }

    /**
     * 通过多边形查询变电站信息
     *
     * @param polygonMercator 墨卡托坐标系的多边形字符串
     * @return 变电站查询结果
     * @throws IOException
     */
    private AmapSdkCommon.PSRByPolygonRspModel querySubstationsByPolygon(String polygonMercator) throws IOException {

        // 需要查询psrType
        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.add("zf01");

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 1).build()).collect(Collectors.toList());
        // 需要接口返回的字段
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(buildParms.size(), buildParms).attrName(Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName"));
        powerGridFilter.setPolygon(polygonMercator);
        powerGridFilter.setSrs("EPSG:3857");

        return AmapSdkCommon.queryPSRByPolygon(powerGridFilter);
    }

    /**
     * 处理变电站数据并补充其他信息
     *
     * @param substationResult 内网接口返回的变电站数据
     * @param feederId         馈线ID
     * @return 数据补充
     */
    private List<NearbySubstationInfoVo> processSubstationData(AmapSdkCommon.PSRByPolygonRspModel substationResult, String feederId) {
        List<NearbySubstationInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : substationResult.getResult().getPsrDataList()) {
                if (!"zf01".equals(psrDataList.getPsrType()) || CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbySubstationInfoVo substationInfo = convertToSubstationInfo(psrDevice);
                        if (substationInfo != null) {
                            result.add(substationInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理变电站{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理变电站数据失败", e);
        }
        return result;
    }

    /**
     * 处理变电站数据并计算距离
     *
     * @param substationResult 内网接口返回的变电站数据
     * @param targetLng        目标点经度
     * @param targetLat        目标点纬度
     * @return 处理后的变电站信息列表，包含距离信息
     */
    private List<NearbySubstationInfoVo> processSubstationDataWithDistance(AmapSdkCommon.PSRByPolygonRspModel substationResult, double targetLng, double targetLat) {
        List<NearbySubstationInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : substationResult.getResult().getPsrDataList()) {
                if (!"zf01".equals(psrDataList.getPsrType()) || CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbySubstationInfoVo substationInfo = convertToSubstationInfo(psrDevice);
                        if (substationInfo != null) {
                            // 计算距离
                            double distance = calculateSubstationDistance(substationInfo, targetLng, targetLat);
                            substationInfo.setDistance(distance);
                            result.add(substationInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理变电站{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理变电站数据失败", e);
        }
        return result;
    }

    /**
     * 计算变电站与目标点的距离
     */
    private double calculateSubstationDistance(NearbySubstationInfoVo substationInfo, double targetLng, double targetLat) {
        if (substationInfo.getLngLat() != null && substationInfo.getLngLat().length >= 2) {
            return GeoDistanceCalculator.calculateDistance(targetLng, targetLat, substationInfo.getLngLat()[0], substationInfo.getLngLat()[1]);
        }
        return Double.MAX_VALUE;
    }

    /**
     * 将PSR设备信息转换为变电站信息VO
     *
     * @param psrDevice PSR设备信息
     * @return 变电站信息VO
     */
    private NearbySubstationInfoVo convertToSubstationInfo(AmapSdkCommon.PSRDevice psrDevice) {
        try {
            NearbySubstationInfoVo substationInfo = new NearbySubstationInfoVo();
            // 设置基础信息
            substationInfo.setName(psrDevice.getPsrName());
            substationInfo.setPsrId(psrDevice.getPsrId());
            substationInfo.setPsrTypeName(psrDevice.getPsrTypeName());
            substationInfo.setVlevelName(psrDevice.getVlevelName());
            substationInfo.setVlevelCode(psrDevice.getVlevelCode());
            substationInfo.setCoordinate(psrDevice.getCoordinate());
            substationInfo.setSwitchStatus(psrDevice.getSwitchStatus());
            substationInfo.setMaintCrew(psrDevice.getMaintCrew());
            substationInfo.setMaintCrewName(psrDevice.getMaintCrewName());
            substationInfo.setCityOrg(psrDevice.getCityOrg());

            String coordinate = psrDevice.getCoordinate();
            if (StringUtils.isNotBlank(coordinate)) {
                String[] coordinates = coordinate.split(" ");
                substationInfo.setLngLat(MercatorLineCircleIntersection.mercatorToLngLat(Double.parseDouble(coordinates[0]), Double.parseDouble(coordinates[1])));
            }
            substationInfo.setGeoPositon(coordinate);

            // 获取emsId，查询剩余间隔
            String emsId = findEmsIdByPsrId(psrDevice.getPsrId());

            // 查询剩余间隔信息
            if (StringUtils.isNotEmpty(emsId)) {
                List<TopoTraceReal> topoTraceReals = bayQueryMapper.selectSwitchBySubstation(emsId);
                if (CollectionUtils.isNotEmpty(topoTraceReals)) {
                    // 过滤备用间隔
                    List<TopoTraceReal> remainingBays = topoTraceReals.stream().filter(t -> StringUtils.isNotBlank(t.getStartBreakName()) && (t.getStartBreakName().contains("备用") || t.getStartBreakName().contains("预留"))).collect(Collectors.toList());
                    substationInfo.setRemainingBayCount(remainingBays.size());
                    // 母线开关信息
                    List<BusbarSwitchVo> busbarSwitchVoList = new ArrayList<>();
                    for (TopoTraceReal topoTraceReal : remainingBays) {
                        BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
                        busbarSwitchVo.setStationPsrId(emsId);
                        busbarSwitchVo.setStationPsrName(psrDevice.getPsrName());
                        busbarSwitchVo.setBusbarId(topoTraceReal.getBusbar());
                        busbarSwitchVo.setBusbarName(topoTraceReal.getBusbarName());
                        busbarSwitchVo.setSwitchId(topoTraceReal.getStartBreak());
                        busbarSwitchVo.setSwitchName(topoTraceReal.getStartBreakName());
                        busbarSwitchVo.setIsSpare(true);
                        busbarSwitchVoList.add(busbarSwitchVo);
                    }
                    substationInfo.setBusbarSwitchVoList(busbarSwitchVoList);
                } else {
                    substationInfo.setRemainingBayCount(0);
                    substationInfo.setBusbarSwitchVoList(new ArrayList<>());
                }
            } else {
                substationInfo.setRemainingBayCount(0);
                substationInfo.setBusbarSwitchVoList(new ArrayList<>());
            }
            return substationInfo;
        } catch (Exception e) {
            log.error("转换变电站信息失败: {}", psrDevice.getPsrName(), e);
            return null;
        }
    }

    /**
     * 通过psrId查找对应的emsId
     *
     * @param psrId PSR设备ID
     * @return emsId，如果未找到返回null
     */
    private String findEmsIdByPsrId(String psrId) {
        try {
            LambdaQueryWrapper<DeviceSubstation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceSubstation::getPsrId, psrId);
            DeviceSubstation localSubstation = deviceSubstation2Mapper.selectOne(queryWrapper);

            if (localSubstation != null) {
                return localSubstation.getEmsId();
            }
            log.debug("未在本地数据库中找到psrId={}对应的变电站", psrId);
            return null;
        } catch (Exception e) {
            log.warn("查找emsId失败，psrId: {}", psrId, e);
            return psrId;
        }
    }

    @Override
    public List<NearbyDeviceInfoVo> queryNearbyDevices(String feederId, double bufferRadius) {
        try {
            AmapSdkCommon.PSRByPolygonRspModel deviceResult;
            if (bdzHandlerMode) {
                log.info("通过内网接口查询附近设备信息，feederId: {}", feederId);
                // 获取馈线的地理坐标信息
                LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
                DeviceFeeder feeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

                if (feeder == null || StringUtils.isEmpty(feeder.getGeoList())) {
                    log.warn("未找到馈线{}的地理坐标", feederId);
                    return new ArrayList<>();
                }
                // 解析馈线坐标并创建缓冲区
                List<List<double[]>> feederLineSegments = CoordinateConverter.split(feeder.getGeoList());
                if (CollectionUtils.isEmpty(feederLineSegments)) {
                    log.warn("馈线{}坐标解析失败", feederId);
                    return new ArrayList<>();
                }
                // 创建缓冲区多边形
                List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(feederLineSegments, bufferRadius);
                // 判断首尾经纬度是否一致，不一致将头添加到尾部
                if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                    bufferPolygon.add(bufferPolygon.get(0));
                }
                // 转换为墨卡托坐标
                String polygonMercator = BufferPolygonCreator.convertToMercator(bufferPolygon);
                // 调用内网接口查询设备
                deviceResult = queryDevicesByPolygon(polygonMercator);
            } else {
                log.info("调用外网模拟接口");
                deviceResult = queryDevicesByPolygonMock(feederId);
            }

            if (deviceResult.getResult() == null || CollectionUtils.isEmpty(deviceResult.getResult().getPsrDataList())) {
                log.info("内网接口未查询到设备数据");
                return new ArrayList<>();
            }
            // 解析设备数据
            List<NearbyDeviceInfoVo> result = processDeviceData(deviceResult, feederId);

            log.info("通过内网接口成功查询到{}个设备", result.size());
            return result;
        } catch (Exception e) {
            log.error("通过内网接口查询附近设备失败，feederId: {}", feederId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据坐标点查询附近变电站
     *
     * @return 附近变电站信息列表，按距离排序
     */
    @Override
    public List<NearbySubstationInfoVo> queryNearbySubstationsByPoint(NearbySubstationQueryBo queryBo) {
        Double lng = queryBo.getLng();
        Double lat = queryBo.getLat();
        Double bufferRadius = queryBo.getBufferRadius();
        try {
            log.info("开始查询坐标点({}, {})半径{}米内的附近变电站", queryBo.getLng(), queryBo.getLat(), bufferRadius);
            // 调用内网接口查询变电站
            AmapSdkCommon.PSRByPolygonRspModel substationResult = querySubstationsByCircle(queryBo.getPoint(), bufferRadius);

            if (substationResult.getResult() == null || CollectionUtils.isEmpty(substationResult.getResult().getPsrDataList())) {
                log.info("未查询到变电站数据");
                return new ArrayList<>();
            }
            // 处理变电站数据并计算距离
            List<NearbySubstationInfoVo> allSubstations = processSubstationDataWithDistance(substationResult, lng, lat);
            // 按距离排序，最近的变电站排在第一位
            allSubstations.sort(Comparator.comparingDouble(NearbySubstationInfoVo::getDistance));
            log.info("成功查询到{}个变电站，最近变电站距离{}米", allSubstations.size(), allSubstations.isEmpty() ? 0 : allSubstations.get(0).getDistance());
            return allSubstations;
        } catch (Exception e) {
            log.error("查询附近变电站失败，坐标点({}, {}), 半径{}米: {}", lng, lat, bufferRadius, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据一个坐标点查询附近物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     * 排除掉当前线路的设备。 剩余的设备根据当前坐标和返回设备的坐标计算距离，判断那条线上的负载率是否正常，不正常排除掉那条线上的设备 最终返回多条符合结果的数据，但是只有一条是最符合的，就是在符合结果集中取最近的，
     *
     * @return 附近设备信息列表，按距离排序，第一个为最符合的设备
     */
    @Override
    public List<NearbyDeviceInfoVo> queryNearbyDevicesByPoint(NearbyDeviceQueryBo queryBo) {
        double lng = queryBo.getLng();
        double lat = queryBo.getLat();
        double bufferRadius = queryBo.getBufferRadius();
        String excludeFeederId = queryBo.getFeederId();
        try {
            log.info("开始查询坐标点({}, {})半径{}米内的附近设备", lng, lat, bufferRadius);
            // 调用内网接口查询设备
            AmapSdkCommon.PSRByPolygonRspModel deviceResult = queryPSRByCircle(queryBo.getPoint(), bufferRadius);

            if (deviceResult.getResult() == null || CollectionUtils.isEmpty(deviceResult.getResult().getPsrDataList())) {
                log.info("内网接口未查询到设备数据");
                return new ArrayList<>();
            }
            // 解析设备数据并计算距离
            List<NearbyDeviceInfoVo> allDevices = processDeviceDataWithDistance(deviceResult, lng, lat);

            if (CollectionUtils.isEmpty(allDevices)) {
                log.info("未找到符合条件的设备");
                return new ArrayList<>();
            }
            //  排除当前线路的设备
            List<NearbyDeviceInfoVo> filteredDevices = excludeCurrentLineDevices(allDevices, excludeFeederId);
            // 过滤负载率异常的线路设备
//            List<NearbyDeviceInfoVo> normalLoadDevices = filterByLoadRate(filteredDevices);
            List<NearbyDeviceInfoVo> normalLoadDevices = filteredDevices;
            // 按距离排序，最近的设备排在第一位
            normalLoadDevices.sort(Comparator.comparingDouble(NearbyDeviceInfoVo::getDistance));
            log.info("成功查询到{}个符合条件的设备，最近设备距离{}米", normalLoadDevices.size(), normalLoadDevices.isEmpty() ? 0 : normalLoadDevices.get(0).getDistance());
            return normalLoadDevices;
        } catch (Exception e) {
            log.error("查询附近设备失败，坐标点({}, {}), 半径{}米: {}", lng, lat, bufferRadius, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据坐标点查询附近变电站
     */
    private AmapSdkCommon.PSRByPolygonRspModel querySubstationsByCircle(String point, double bufferRadius) throws IOException {
        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.add("zf01"); // 变电站类型

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 1).build()).collect(Collectors.toList());

        // 需要接口返回的字段
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(buildParms.size(), buildParms).attrName(Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName"));
        powerGridFilter.getPsrQueryInfo().setPsrQueryList(buildParms);

        powerGridFilter.setRadius(bufferRadius);
        powerGridFilter.setPoint(point);
        powerGridFilter.setSrs("EPSG:3857");
        powerGridFilter.setFlag("1");
        return AmapSdkCommon.queryPSRByCircle(powerGridFilter);
    }

    private AmapSdkCommon.PSRByPolygonRspModel queryPSRByCircle(String point, double bufferRadius) throws IOException {
        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.addAll(HWG_TYPES);
        psrTypeList.addAll(POLE_TYPES);
        psrTypeList.addAll(KGZ_TYPES);

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 0).build()).collect(Collectors.toList());
        // 需要接口返回的字段
        List<String> attr = Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName", "feederId", "feederName");
        AmapSdkCommon.QueryPSRByCircleFilter powerGridFilter = new AmapSdkCommon.QueryPSRByCircleFilter();
        powerGridFilter.getPsrQueryInfo().setAttrNameList(attr);
        powerGridFilter.getPsrQueryInfo().setPsrQueryList(buildParms);
        powerGridFilter.setRadius(bufferRadius);
        powerGridFilter.setPoint(point);
        powerGridFilter.setSrs("EPSG:3857");
        return AmapSdkCommon.queryPSRByCircle(powerGridFilter);
    }

    /**
     * 处理设备数据并计算距离
     *
     * @param deviceResult 内网接口返回的设备数据
     * @param targetLng    目标点经度
     * @param targetLat    目标点纬度
     * @return 处理后的设备信息列表，包含距离信息
     */
    private List<NearbyDeviceInfoVo> processDeviceDataWithDistance(AmapSdkCommon.PSRByPolygonRspModel deviceResult, double targetLng, double targetLat) {
        List<NearbyDeviceInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : deviceResult.getResult().getPsrDataList()) {
                if (CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbyDeviceInfoVo deviceInfo = convertToDeviceInfo(psrDevice);
                        if (deviceInfo != null) {
                            // 计算距离
                            double distance = calculateDistance(deviceInfo, targetLng, targetLat);
                            deviceInfo.setDistance(distance);
                            result.add(deviceInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理设备{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理设备数据失败", e);
        }
        return result;
    }

    /**
     * 计算设备与目标点的距离
     *
     * @param deviceInfo 设备信息
     * @param targetLng  目标点经度
     * @param targetLat  目标点纬度
     * @return 距离（米）
     */
    private double calculateDistance(NearbyDeviceInfoVo deviceInfo, double targetLng, double targetLat) {
        if (deviceInfo.getLngLat() == null || deviceInfo.getLngLat().length < 2) {
            return Double.MAX_VALUE;
        }
        double deviceLng = deviceInfo.getLngLat()[0];
        double deviceLat = deviceInfo.getLngLat()[1];
        // 使用Haversine公式计算距离
        return GeoDistanceCalculator.calculateDistance(targetLat, targetLng, deviceLat, deviceLng);
    }

    /**
     * 排除当前线路的设备
     *
     * @param devices         设备列表
     * @param excludeFeederId 需要排除的线路ID
     * @return 过滤后的设备列表
     */
    private List<NearbyDeviceInfoVo> excludeCurrentLineDevices(List<NearbyDeviceInfoVo> devices, String excludeFeederId) {
        if (StringUtils.isEmpty(excludeFeederId) || CollectionUtils.isEmpty(devices)) {
            return devices;
        }
        return devices.stream().filter(device -> {
            List<String> feederIds = device.getFeederIds();
            if (CollectionUtils.isEmpty(feederIds)) {
                // 填充为空的feederId
                List<Map<String, Object>> params = new ArrayList<>();
                params.add(MapUtil.<String, Object>builder()
                        .put("psrId", device.getPsrId())
                        .put("psrType", device.getPsrType())
                        .put("distribution", "0").build());
                AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(params.size(), params).attrName(Arrays.asList("psrId", "psrType", "coordinate", "psrName","feederId"));

                try {
                    List<AmapSdkCommon.DeviceRspModel<AmapSdkCommon.DeviceInfo>> deviceRspModels = AmapSdkCommon.queryDeviceById(powerGridFilter);
                    if (deviceRspModels.size()==0){
                        return false;
                    }
                    String feederId = deviceRspModels.get(0).getPsrList().get(0).getFeederId();
                    device.setFeederId(feederId);
                    return !device.getFeederIds().contains(excludeFeederId);
                } catch (IOException e) {
                    log.warn("根据设备id查询异常:psrId：{}", device.getPsrId());
                    return false;
                }
            }
            // 排除包含当前线路ID的设备
            return !feederIds.contains(excludeFeederId);
        }).collect(Collectors.toList());
    }

    /**
     * 根据负载率过滤设备，排除负载率异常的线路上的设备
     *
     * @param devices 设备列表
     * @return 过滤后的设备列表
     */
    private List<NearbyDeviceInfoVo> filterByLoadRate(List<NearbyDeviceInfoVo> devices) {
        if (CollectionUtils.isEmpty(devices)) {
            return devices;
        }
        try {
            // 获取最大负载率阈值
            double maxLoadRate = contactHandleService.getMaxFeederLoad();
            // 收集所有设备关联的线路ID
            Set<String> allFeederIds = devices.stream().flatMap(device -> device.getFeederIds().stream()).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            if (allFeederIds.isEmpty()) {
                log.info("设备列表中没有关联线路信息，返回所有设备");
                return devices;
            }
            // 查询线路负载率信息
            List<FeederNtVo> feederNtList = feederDeviceMapper.selectFeederNtsByFeederIds(new ArrayList<>(allFeederIds));
            // 过滤出负载率正常的线路ID
            Set<String> normalFeederIds = feederNtList.stream().filter(feederNt -> feederNt.getHisMaxLoadRate() != null && feederNt.getHisMaxLoadRate() < maxLoadRate).map(FeederNtVo::getPsrId).collect(Collectors.toSet());

            // 过滤设备：保留没有关联线路或关联正常负载率线路的设备
            return devices.stream().filter(device -> {
                List<String> feederIds = device.getFeederIds();
                if (CollectionUtils.isEmpty(feederIds)) {
                    return true;
                }
                // 检查是否有至少一条关联线路的负载率正常
                return feederIds.stream().anyMatch(normalFeederIds::contains);
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据负载率过滤设备失败: {}", e.getMessage(), e);
            return devices;
        }
    }

    /**
     * 外网模拟查询设备
     *
     * @param feederId
     * @return
     */
    private AmapSdkCommon.PSRByPolygonRspModel queryDevicesByPolygonMock(String feederId) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("feederId", feederId);
            requestBody.put("type", "GHB");
            return AmapSdkCommon.queryPSRByPolygon(requestBody);
        } catch (Exception e) {
            log.error("调用外网模拟接口失败", e);
        }
        return null;
    }

    /**
     * 通过多边形查询设备信息
     *
     * @param polygonMercator 墨卡托坐标系的多边形字符串
     * @return 设备查询结果
     * @throws IOException
     */
    private AmapSdkCommon.PSRByPolygonRspModel queryDevicesByPolygon(String polygonMercator) throws IOException {
        // 构建请求体
        List<String> psrTypeList = new ArrayList<>();
        psrTypeList.addAll(HWG_TYPES);
        psrTypeList.addAll(POLE_TYPES);
        psrTypeList.addAll(KGZ_TYPES);

        List<Map<String, Object>> buildParms = psrTypeList.stream().map(psrType -> MapUtil.<String, Object>builder().put("psrType", psrType).put("whereClause", "1=1").put("attrNameList", null).put("distribution", 0).build()).collect(Collectors.toList());
        // 需要接口返回的字段
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(buildParms.size(), buildParms).attrName(Arrays.asList("psrId", "psrName", "psrType", "psrTypeName", "coordinate", "vlevelName", "vlevelCode", "maintCrew", "maintCrewName", "maintOrg", "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "zoneName"));
        powerGridFilter.setPolygon(polygonMercator);
        powerGridFilter.setSrs("EPSG:3857");

        return AmapSdkCommon.queryPSRByPolygon(powerGridFilter);
    }

    /**
     * 处理设备数据
     *
     * @param deviceResult 内网接口返回的设备数据
     * @param feederId     馈线ID
     * @return 处理后的设备信息列表
     */
    private List<NearbyDeviceInfoVo> processDeviceData(AmapSdkCommon.PSRByPolygonRspModel deviceResult, String feederId) {
        List<NearbyDeviceInfoVo> result = new ArrayList<>();

        try {
            for (AmapSdkCommon.PSRDataList psrDataList : deviceResult.getResult().getPsrDataList()) {
                if (CollectionUtils.isEmpty(psrDataList.getPsrList())) {
                    continue;
                }
                for (AmapSdkCommon.PSRDevice psrDevice : psrDataList.getPsrList()) {
                    try {
                        NearbyDeviceInfoVo deviceInfo = convertToDeviceInfo(psrDevice);
                        if (deviceInfo != null) {
                            result.add(deviceInfo);
                        }
                    } catch (Exception e) {
                        log.warn("处理设备{}数据失败: {}", psrDevice.getPsrName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理设备数据失败", e);
        }
        return result;
    }

    /**
     * 将PSR设备信息转换为设备信息VO
     *
     * @param psrDevice PSR设备信息
     * @return 设备信息VO
     */
    private NearbyDeviceInfoVo convertToDeviceInfo(AmapSdkCommon.PSRDevice psrDevice) {
        try {
            NearbyDeviceInfoVo deviceInfo = new NearbyDeviceInfoVo();
            // 设置基础信息
            deviceInfo.setPsrId(psrDevice.getPsrId());
            deviceInfo.setPsrType(psrDevice.getPsrType());
            deviceInfo.setPsrTypeName(psrDevice.getPsrTypeName());
            deviceInfo.setPsrName(psrDevice.getPsrName());
            deviceInfo.setZoneId(psrDevice.getZoneId());
            deviceInfo.setZoneName(psrDevice.getZoneName());
            deviceInfo.setVlevelName(psrDevice.getVlevelName());
            deviceInfo.setVlevelCode(psrDevice.getVlevelCode());

            String coordinate = psrDevice.getCoordinate();
            if (StringUtils.isNotBlank(coordinate)) {
                String[] coordinates = coordinate.split(" ");
                if (coordinates.length == 2) {
                    deviceInfo.setLngLat(MercatorLineCircleIntersection.mercatorToLngLat(Double.parseDouble(coordinates[0]), Double.parseDouble(coordinates[1])));
                }
            }
            deviceInfo.setCoordinate(coordinate);

            deviceInfo.setFeederId(psrDevice.getFeederId());
            deviceInfo.setFeederName(psrDevice.getFeederName());
            deviceInfo.setChargedState(psrDevice.getChargedState());
            deviceInfo.setSwitchStatus(psrDevice.getSwitchStatus());
            deviceInfo.setDistribution(psrDevice.getDistribution());
            deviceInfo.setMaintCrew(psrDevice.getMaintCrew());
            deviceInfo.setMaintCrewName(psrDevice.getMaintCrewName());
            deviceInfo.setMaintOrg(psrDevice.getMaintOrg());
            deviceInfo.setMaintOrgName(psrDevice.getMaintOrgName());
            deviceInfo.setCityOrg(psrDevice.getCityOrg());
            deviceInfo.setCityOrgName(psrDevice.getCityOrgName());
            deviceInfo.setProvinceId(psrDevice.getProvinceId());
            deviceInfo.setCrossFeederId(psrDevice.getCrossfeederid());
            deviceInfo.setCrossFeederProvince(psrDevice.getCrossfeederprovince());
            deviceInfo.setIsProvinceContainer(psrDevice.getIsprovincecontainer());
            deviceInfo.setDirection(psrDevice.getDirection());
            deviceInfo.setSiteName(psrDevice.getSiteName());

            // 对于环网柜和开关站，查询剩余间隔信息
            if (HWG_TYPES.contains(psrDevice.getPsrType()) || KGZ_TYPES.contains(psrDevice.getPsrType())) {
                queryRemainingBayInfo(deviceInfo, psrDevice);
            } else {
                // 其他设备类型不需要间隔信息
                deviceInfo.setRemainingBayCount(0);
                deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
            }

            return deviceInfo;

        } catch (Exception e) {
            log.error("转换设备信息失败: {}", psrDevice.getPsrName(), e);
            return null;
        }
    }

    /**
     * 查询环网柜和开关站的剩余间隔信息
     * TODO 这里后面查接口  不走本地的库了
     *
     * @param deviceInfo 设备信息VO
     * @param psrDevice  PSR设备信息
     */
    private void queryRemainingBayInfo(NearbyDeviceInfoVo deviceInfo, AmapSdkCommon.PSRDevice psrDevice) {
        try {
            String psrId = deviceInfo.getPsrId();
            if (StringUtils.isNotEmpty(psrId)) {
                // 通过开关站/环网柜查剩余开关 这里你只查询了0305  0306那些都没查
                List<StationKg> stationKgs = kgMapper.selectStationKgByStationId(psrId);

                if (CollectionUtils.isNotEmpty(stationKgs)) {
                    // 查询剩余开关
                    List<StationKg> spareBreakerList = stationKgs.stream().filter(b -> b.getName().contains("备用") || b.getName().contains("预留")).collect(Collectors.toList());
                    // 构建开关信息
                    deviceInfo.setRemainingBayCount(spareBreakerList.size());
                    List<BusbarSwitchVo> busbarSwitchVoList = new ArrayList<>();
                    for (StationKg breaker : spareBreakerList) {
                        BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
                        busbarSwitchVo.setFeederId(deviceInfo.getFeederId());
                        busbarSwitchVo.setFeederName(deviceInfo.getFeederName());
                        busbarSwitchVo.setSwitchId(breaker.getPsrId());
                        busbarSwitchVo.setSwitchType(breaker.getPsrType());
                        busbarSwitchVo.setSwitchName(breaker.getName());
                        busbarSwitchVo.setIsSpare(true);
                        busbarSwitchVo.setStationPsrType(deviceInfo.getPsrType());
                        busbarSwitchVoList.add(busbarSwitchVo);
                        deviceInfo.setBusbarSwitchVoList(busbarSwitchVoList);
                    }
                } else {
                    deviceInfo.setRemainingBayCount(0);
                    deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
                }
            } else {
                log.debug("未找到设备{}的psrId", psrDevice.getPsrId());
                deviceInfo.setRemainingBayCount(0);
                deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
            }
        } catch (Exception e) {
            log.warn("查询设备{}剩余间隔信息失败: {}", psrDevice.getPsrId(), e.getMessage());
            deviceInfo.setRemainingBayCount(0);
            deviceInfo.setBusbarSwitchVoList(new ArrayList<>());
        }
    }

    /**
     * 计算两个设备坐标的距离信息和B设备的详细信息
     */
    @Override
    public DeviceDistanceInfoVo calculateDeviceDistanceInfo(DeviceDistanceQueryBo queryBo) {
        DeviceDistanceInfoVo result = new DeviceDistanceInfoVo();
        try {
            // 计算直线距离
            double straightDistance = GeoDistanceCalculator.calculateDistance(queryBo.getALat(), queryBo.getALng(), queryBo.getBLat(), queryBo.getBLng());
            result.setStraightLineDistance(straightDistance);
            // 计算规划路径长度
            double[] starts = {queryBo.getALng(), queryBo.getALat()};
            double[] ends = {queryBo.getBLng(), queryBo.getBLat()};
            CalcRouteVo calcRouteVo = mapService.mapRoute(starts, ends, queryBo.getToken());
            result.setLngLatList(calcRouteVo.getLngLatList());
            result.setPlannedRouteDistance(calcRouteVo.getTotalLength());
            // 获取B设备的详细信息
            populateDeviceInfo(result, queryBo);
        } catch (Exception e) {
            log.error("计算设备距离信息失败", e);
        }
        return result;
    }


    /**
     * 填充设备的信息
     */
    private void populateDeviceInfo(DeviceDistanceInfoVo result, DeviceDistanceQueryBo queryBo) {
        try {
            // 获取设备所属线路信息
            String feederId = queryBo.getBfeederId();
            if (StringUtils.isNotBlank(feederId)) {
                result.setFeederId(feederId);
                // 获取线路名称和相关信息
                FeederNtVo feederInfo = feederDeviceMapper.selectFeederNtsByFeederId(feederId);
                if (feederInfo != null) {
                    result.setFeederName(feederInfo.getName());
                    result.setInstalledCapacity(feederInfo.getFeederRateCapacity());
                    result.setMaxHistoryLoadRate(feederInfo.getHisMaxLoadRate());
                    result.setMaxHistoryLoadRateDate(feederInfo.getHisMaxDate());
                    result.setMaxHistoryCurrent(feederInfo.getHisMaxCurrent());
                }
                // 获取变电站和主变信息
                DeviceSubstation substation = feederDeviceMapper.selectDeviceSubstationByFeederId(feederId);
                if (substation != null) {
                    result.setSubstationId(substation.getPsrId());
                    result.setSubstationName(substation.getName());
                    // 主变
                    assert feederInfo != null;
                    result.setTransformerName(feederInfo.getMainByqName());
                }
            }
        } catch (Exception e) {
            log.error("获取设备详细信息失败", e);
        }
    }


}
