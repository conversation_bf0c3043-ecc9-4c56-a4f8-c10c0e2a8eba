package com.ruoyi.service.map;




import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.map.bo.DeviceDistanceQueryBo;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.graph.vo.NodeVo;

import java.util.List;

public interface IMapService {


    List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo);

    /**
     * 根据杆塔id查询两端导线
     */
    List<WireEndVo> selectWireEnd(String psrId);


    /**id和设备类型判断他的真正psrId
     */
    String selectPsrId(String psrId, String psrType);

    /**
     * 查询运方调整转供路径
     */
    List<NodeVo> transferSupply(String feederId, String closeId, String openId);

    /**
     * 获取联络开关信息列表
     *
     * @param feederId 线路ID
     * @return 联络开关信息列表
     */
    List<ContactSwitchInfoVo> getContactSwitchInfo(String feederId);

    /**
     * 计算基于转供路径的负载率变化
     * 根据转供路径计算当前线路转供至另一条线路的负载率变化
     *
     * @param feederId 源线路ID
     * @param closeId  联络开关ID（需要闭合的开关）
     * @param openId   主干开关ID（需要断开的开关）
     * @return 负载率变化计算结果
     */
    FeederTransferCap calculateLoadRateChange(String feederId, String closeId, String openId);

    /**
     * 计算路径
     * @param strPoint
     * @param endPoint
     * @param token
     * @return
     */
    public CalcRouteVo mapRoute(double[] strPoint, double[] endPoint, String token);

}

